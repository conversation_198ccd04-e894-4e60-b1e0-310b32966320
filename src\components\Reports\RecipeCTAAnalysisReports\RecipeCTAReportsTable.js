'use client';
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box } from '@mui/material';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import ContentLoader from '@/components/UI/ContentLoader';
import { getPublicCTAAnalytics } from '@/services/recipeService';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';

export default function RecipeCTAReportsTable({
  searchValue = '',
  filters = {},
  onPaginationChange,
}) {
  // State management
  const [loader, setLoader] = useState(false);
  const [ctaData, setCTAData] = useState([]);
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [sortOrder, setSortOrder] = useState({
    key: '',
    value: 'ASC',
  });

  // Ref to track if we're currently sorting to prevent duplicate API calls
  const isSortingRef = useRef(false);

  // Get CTA Analytics data from API
  const getCTAAnalyticsData = useCallback(
    async (
      search = '',
      pageNo = 1,
      Rpp = rowsPerPage,
      filterData = {},
      Sort = sortOrder,
      showLoader = true
    ) => {
      if (showLoader) {
        setLoader(true);
      }
      try {
        // Create filter object with search as recipe_name and only required filters
        const apiFilter = {
          ...(search && { recipe_name: search }),
          ...(filterData?.ctaType && { cta_type: filterData?.ctaType }),
          ...(filterData?.dateRange && { date_range: filterData?.dateRange }),
        };

        const { data, totalRecords } = await getPublicCTAAnalytics(
          '', // Don't pass search as separate parameter
          pageNo,
          apiFilter,
          Rpp,
          Sort || { key: '', value: 'ASC' } // Use provided sort or default
        );

        const transformedData = data?.map((item, index) => ({
          id: `${item?.recipe_id}-${item?.cta_type?.replace(/\s+/g, '_')}`,
          sequentialId: (pageNo - 1) * Rpp + index + 1,
          recipeName: item?.recipe_name || '-',
          ctaType: item?.cta_type || '-',
          clicks: item?.clicks || 0,
          lastClickedAt: item?.last_clicked_at,
        }));

        setCTAData(transformedData || []);
        setTotalCount(totalRecords || 0);
        setPage(pageNo);
      } catch (error) {
        setApiMessage(
          'error',
          error?.response?.data?.message || 'Failed to fetch CTA analytics data'
        );
        setCTAData([]);
        setTotalCount(0);
      } finally {
        if (showLoader) {
          setLoader(false);
        }
      }
    },
    []
  );

  // Handle sorting
  const handleSort = (key) => {
    const currentDirection = sortOrder?.key === key ? sortOrder?.value : null;
    let newDirection;

    if (currentDirection === 'ASC') {
      newDirection = 'DESC';
    } else if (currentDirection === 'DESC') {
      newDirection = 'ASC';
    } else {
      newDirection = 'ASC';
    }

    const newSortOrder = { key, value: newDirection };

    // Set sorting flag to prevent useEffect from making duplicate call
    isSortingRef.current = true;

    setSortOrder(newSortOrder);
    setPage(1);

    // Call API directly for sorting without loader
    getCTAAnalyticsData(
      searchValue,
      1,
      rowsPerPage,
      filters,
      newSortOrder,
      false
    ).finally(() => {
      // Reset sorting flag after API call completes
      isSortingRef.current = false;
    });
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    // API call will be triggered by useEffect when page changes
  };

  // Handle rows per page change
  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    // API call will be triggered by useEffect when page/rowsPerPage changes
  };

  // Load data when component mounts or filters change
  useEffect(() => {
    // Skip API call if we're currently sorting (handleSort will make the call)
    if (!isSortingRef.current) {
      getCTAAnalyticsData(searchValue, page, rowsPerPage, filters, sortOrder);
    }
  }, [searchValue, filters, sortOrder, page, rowsPerPage, getCTAAnalyticsData]);

  // Notify parent component when pagination changes
  useEffect(() => {
    if (onPaginationChange) {
      onPaginationChange({
        page: page,
        size: rowsPerPage,
      });
    }
  }, [page, rowsPerPage, onPaginationChange]);

  // CommonTable columns configuration
  const columns = [
    {
      header: 'ID',
      accessor: 'sequentialId',
      sortable: false,
    },
    {
      header: 'Recipe Name',
      accessor: 'recipeName',
      sortable: true,
      sortKey: 'recipe_title', // API sort key
      renderCell: (value) => value,
    },
    {
      header: 'CTA Type',
      accessor: 'ctaType',
      sortable: false,
      renderCell: (value) => value,
    },
    {
      header: 'Clicks',
      accessor: 'clicks',
      sortable: true,
      sortKey: 'clicks', // API sort key
      renderCell: (value) => value,
    },
    {
      header: 'Last Clicked At',
      accessor: 'lastClickedAt',
      sortable: true,
      sortKey: 'created_at', // API sort key
      renderCell: (value) => (value ? DateFormat(value, 'datesWithhour') : '-'),
    },
  ];

  return (
    <Box className="report-table-container">
      {loader ? (
        <ContentLoader />
      ) : (
        <CommonTable
          columns={columns}
          data={ctaData}
          totalCount={totalCount}
          currentPage={page}
          rowsPerPage={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          showPagination={true}
          onSort={handleSort}
          sortOrder={sortOrder}
        />
      )}
    </Box>
  );
}
