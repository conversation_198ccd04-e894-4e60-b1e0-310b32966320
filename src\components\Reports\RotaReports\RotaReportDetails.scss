@import '@/app/_globals.scss';

// DialogBox styles for RotaReportDetails
.rota-details-dialog {
  // .MuiDialog-paperScrollPaper {
  //   max-width: 1200px;
  //   width: 100%;
  //   max-height: 90vh;

  //   @media (max-width: 1200px) {
  //     max-width: 95vw;
  //   }

  //   @media (max-width: 768px) {
  //     max-width: calc(100% - 32px);
  //     max-height: 95vh;
  //   }
  // }

  // Employee header info in dialog title
  .employee-header-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) 0;

    .employee-avatar {
      width: var(--icon-size-lg);
      height: var(--icon-size-lg);
      background-color: var(--color-primary);
      color: var(--text-color-white);
      font-size: var(--font-size-lg);
    }

    .employee-details {
      .employee-name {
        font-family: var(--font-family-primary);
        font-weight: var(--font-weight-semibold);
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-xs);
        color: var(--text-color-primary);
      }

      .employee-id {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        color: var(--text-color-secondary);
        margin-bottom: var(--spacing-xs);
      }

      .employee-role {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        color: var(--text-color-secondary);
      }
    }
  }

  // Main content area
  .rota-details-content {
    padding: 0;

    .summary-card {
      margin-bottom: var(--spacing-lg);
      border-radius: var(--border-radius-md);
      box-shadow: var(--box-shadow-xs);
      border: var(--normal-sec-border);

      .summary-item {
        text-align: center;
        padding: var(--spacing-sm);

        .MuiTypography-body2 {
          color: var(--text-color-slate-gray);
          margin-bottom: var(--spacing-sm);
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
        }

        .MuiTypography-h6 {
          font-family: var(--font-family-primary);
          font-weight: var(--font-weight-semibold);
          margin-top: var(--spacing-sm);
          color: var(--text-color-black);
        }

        .MuiTypography-body1 {
          font-family: var(--font-family-primary);
          font-weight: var(--font-weight-medium);
          color: var(--text-color-black);
        }
      }
    }

    // Tabs section styles
    .details-tabs-section {
      margin-top: var(--spacing-lg);

      .tab-content {
        margin-top: var(--spacing-md);
      }
    }

    // Tab content styles
    .shifts-card,
    .leaves-card,
    .daysoff-card {
      .MuiCardContent-root {
        padding: var(--spacing-lg);
      }

      .MuiTableContainer-root {
        border-radius: var(--border-radius-md);
        box-shadow: var(--box-shadow-xs);
        border: var(--normal-sec-border);
      }

      .MuiTableHead-root {
        .MuiTableCell-root {
          background-color: var(--color-off-white);
          font-weight: var(--font-weight-semibold);
          color: var(--text-color-black);
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
          padding: var(--spacing-lg);
          border-bottom: var(--normal-sec-border);
        }
      }

      .MuiTableBody-root {
        .MuiTableRow-root {
          &:nth-of-type(even) {
            background-color: var(--color-off-white);
          }

          &:hover {
            background-color: var(--color-background-hover);
          }

          .MuiTableCell-root {
            font-family: var(--font-family-primary);
            color: var(--text-color-black);
            font-size: var(--font-size-sm);
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: var(--normal-sec-border);
          }
        }
      }

      .MuiChip-root {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
        border-radius: var(--border-radius-xs);

        &.MuiChip-colorPrimary {
          background-color: var(--color-primary);
          color: var(--text-color-white);
        }

        &.MuiChip-colorSecondary {
          background-color: var(--color-secondary);
          color: var(--text-color-white);
        }

        &.MuiChip-colorSuccess {
          background-color: var(--color-success);
          color: var(--text-color-white);
        }

        &.MuiChip-colorWarning {
          background-color: var(--color-warning);
          color: var(--text-color-white);
        }

        &.MuiChip-colorError {
          background-color: var(--color-error);
          color: var(--text-color-white);
        }

        &.MuiChip-colorInfo {
          background-color: var(--color-info);
          color: var(--text-color-white);
        }

        &.MuiChip-colorDefault {
          background-color: var(--color-slate-gray);
          color: var(--text-color-white);
        }
      }

      .MuiTable-root {
        min-width: 600px; // Enable horizontal scroll on small screens
      }

      // Specific table styling for different tabs
      &.swap-card .swap-table .MuiTable-root {
        min-width: 800px; // More columns for swap data
      }

      &.drop-card .drop-table .MuiTable-root {
        min-width: 900px; // More columns for drop data
      }
    }

    // Filter section styling
    .filter-card {
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-md);

      .MuiCardContent-root {
        padding: var(--spacing-md);

        &:last-child {
          padding-bottom: var(--spacing-md);
        }
      }

      .MuiTextField-root {
        .MuiInputBase-root {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
        }

        .MuiInputLabel-root {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
        }
      }

      .MuiButton-root {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        text-transform: none;
        border-radius: var(--border-radius-sm);
      }
    }

    // Enhanced shifts table for combined data
    .shifts-card {
      .shifts-table {
        .MuiTable-root {
          min-width: 900px; // More columns for combined data
        }

        .MuiTableCell-root {
          &:first-child {
            min-width: 80px; // Type column
          }

          &:nth-child(6) {
            min-width: 150px; // Details column
          }
        }
      }

      // NoDataView styling within tables
      .MuiTableCell-root {
        &:has(.no-data-wrap) {
          border: none !important;
          padding: 0 !important;
        }
      }

      .no-data-wrap {
        height: 200px;
        margin-top: var(--spacing-md);
        margin-bottom: var(--spacing-md);

        .no-data-title {
          font-size: var(--font-size-md);
          margin-bottom: var(--spacing-xs);
        }

        .no-data-description {
          font-size: var(--font-size-sm);
        }
      }
    }

    // Data cards container styling
    .data-cards-container {
      margin-top: var(--spacing-md);

      .data-item-card {
        border: var(--normal-sec-border);
        border-radius: var(--border-radius-lg);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease-in-out;
        height: 100%;
        background: var(--color-white);
        overflow: hidden;

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
          transform: translateY(-4px);
        }

        .MuiCardContent-root {
          padding: var(--spacing-lg);
          height: 100%;

          &:last-child {
            padding-bottom: var(--spacing-lg);
          }
        }

        // Header section with border
        .MuiBox-root:first-child {
          border-bottom: 1px solid #f0f0f0;
          padding-bottom: var(--spacing-sm);
          margin-bottom: var(--spacing-md);
        }

        .type-chip {
          font-weight: 700;
          font-size: var(--font-size-xs);
          text-transform: uppercase;
          letter-spacing: 1px;
          border-width: 2px;
          border-style: solid;
          padding: 4px 12px;
        }

        // Label styling (uppercase labels)
        .MuiTypography-caption {
          font-family: var(--font-family-primary);
          font-size: 10px;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 1px;
          color: #8e8e93;
          margin-bottom: 4px;
          line-height: 1.2;
        }

        // Value styling
        .MuiTypography-body1 {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-md);
          color: var(--text-color-primary);
          font-weight: 600;
          line-height: 1.4;
          margin-bottom: 0;
        }

        // Secondary text styling
        .MuiTypography-body2 {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-sm);
          color: #8e8e93;
          font-weight: 500;
          line-height: 1.4;
        }

        .MuiChip-root {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-xs);
          font-weight: 600;
          border-radius: var(--border-radius-sm);

          &.type-chip {
            border-width: 2px;
            border-style: solid;
          }
        }

        // Content sections spacing
        .MuiBox-root {
          &:not(:first-child) {
            margin-bottom: var(--spacing-sm);

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        // Responsive adjustments
        @media (max-width: 768px) {
          .MuiCardContent-root {
            padding: var(--spacing-md);
          }

          .MuiTypography-body1 {
            font-size: var(--font-size-sm);
          }

          .MuiTypography-caption {
            font-size: 9px;
          }
        }

        @media (max-width: 480px) {
          .MuiCardContent-root {
            padding: var(--spacing-sm);
          }
        }
      }
    }

    // No data container styling
    .no-data-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      background: var(--color-white);
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-md);
      margin-top: var(--spacing-md);
    }
  }
}
