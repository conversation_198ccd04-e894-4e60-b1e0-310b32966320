'use client';
import React, { useState, useEffect, useContext, useCallback } from 'react';
import { Box, Typography } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import DialogBox from '@/components/UI/Modalbox';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { reportsService } from '@/services/reportService';

const filterFields = [
  {
    type: 'search',
    label: 'Search',
    name: 'search',
    placeholder: 'Search',
  },
  {
    type: 'date-range',
    label: 'Date Range',
    name: 'dateRange',
    placeholder: 'Select date range',
    format: 'MMM dd, yyyy',
  },
];

export default function ActivityReports({ onFiltersUpdate }) {
  const { authState, setUserdata } = useContext(AuthContext);

  const [activityList, setActivityList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [toggleModal, setToggleModal] = useState(false);
  const [userAgentValue, setUserAgentValue] = useState('');
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });

  // Menu items for action dropdown
  const menuItems = [
    {
      label: 'User Agent',
      icon: <Icon name="Eye" size={16} />,
      onClick: (_, rowData) => {
        setToggleModal(!toggleModal);
        setUserAgentValue({
          userAgent: rowData?.userAgent,
        });
      },
    },
  ];

  // CommonTable columns (keeping same structure)
  const columns = [
    {
      header: 'ID',
      accessor: 'user_id',
      sortable: true,
    },
    {
      header: 'Name',
      accessor: 'user_full_name',
      sortable: false,
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row?.users}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          authState={authState}
          setUserdata={setUserdata}
        />
      ),
    },
    {
      header: 'Date & Time',
      accessor: 'createdAt',
      sortable: false,
      renderCell: (value) => DateFormat(value, 'datesWithhour'),
    },
    {
      header: 'Activity actions',
      accessor: 'activity_action',
      sortable: false,
      renderCell: (_, row) =>
        `${row?.activity_table ? row?.activity_table : ''} ${row?.activity_action ? row?.activity_action : ''}`.trim(),
    },
    {
      header: 'IP',
      accessor: 'ip_address',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'Location',
      accessor: 'location',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'Address',
      accessor: 'address',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    // User agent column is now handled by CommonTable's actionMenuItems prop
  ];

  // Handle sorting
  const handleSort = (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setPage(1);

    const startDate = filters.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Call API with new sort order without showing loader
    getActivityDetails(
      searchValue,
      1,
      rowsPerPage,
      startDate,
      endDate,
      key,
      newOrder,
      false // Don't show loader for sorting
    );
  };

  // Get activity details from API (same as Activity Logs)
  const getActivityDetails = useCallback(
    async (
      search = '',
      pageNo = 1,
      Rpp = rowsPerPage,
      startDate = '',
      endDate = '',
      sortBy = '',
      sortOrderValue = '',
      showLoader = true
    ) => {
      if (showLoader) setLoader(true);
      try {
        // Use the new consolidated reports service
        const filters = {
          search,
          page: pageNo,
          size: Rpp,
          start_date: startDate,
          end_date: endDate,
          sort_by: sortBy,
          sort_order: sortOrderValue,
        };

        const response = await reportsService.getActivityReportsList(filters);

        if (response.success) {
          const activityData =
            response.data &&
            response.data.length > 0 &&
            response.data.map((a, index) => {
              const newdata = JSON.parse(a?.new_data);
              return {
                ...a,
                user_id: a?.users?.id,
                id: index,
                user_full_name: a?.users?.user_full_name,
                user_email: a?.users?.user_email,
                branch_name: newdata?.branch_name ? newdata?.branch_name : '-',
                ip_address: a?.ip_address ? a?.ip_address : '-',
                new_data: newdata,
                department_name: newdata?.department_name
                  ? newdata?.department_name
                  : '-',
              };
            });
          setPage(response.page);
          setTotalCount(response.count);
          setActivityList(activityData ? activityData : []);
          setLoader(false);
        } else {
          setLoader(false);
          setActivityList([]);
          setApiMessage('error', response.message);
        }
      } catch (error) {
        setLoader(false);
        setActivityList([]);
        setApiMessage(
          'error',
          error?.message || 'Failed to fetch activity reports'
        );
      }
    },
    [rowsPerPage]
  );

  // Handle filter apply
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values.search || '');
    const searchTerm = values.search || '';

    // Extract date range values - CustomDateRangePicker returns [startDate, endDate] array
    const startDate = values.dateRange?.[0]
      ? new Date(values.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = values.dateRange?.[1]
      ? new Date(values.dateRange[1]).toISOString().split('T')[0]
      : '';

    // Prepare filters for export
    const exportFilters = {
      search: searchTerm,
      start_date: startDate,
      end_date: endDate,
      sort_by: sortOrder?.key || '',
      sort_order: sortOrder?.value || '',
    };

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }

    setPage(1);
    getActivityDetails(
      searchTerm,
      1,
      rowsPerPage,
      startDate,
      endDate,
      sortOrder?.key || '',
      sortOrder?.value || '',
      false // Don't show loader for filter operations
    );
  };

  // Handle pagination (Rota Reports style)
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const searchTerm = filters.search || '';
    const startDate = filters.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';
    getActivityDetails(
      searchTerm,
      newPage,
      rowsPerPage,
      startDate,
      endDate,
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const searchTerm = filters.search || '';
    const startDate = filters.dateRange?.[0]
      ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
      : '';
    const endDate = filters.dateRange?.[1]
      ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
      : '';
    getActivityDetails(
      searchTerm,
      1,
      newRowsPerPage,
      startDate,
      endDate,
      sortOrder?.key || '',
      sortOrder?.value || ''
    );
  };

  // Initial load
  useEffect(() => {
    getActivityDetails();
  }, [getActivityDetails]);

  // Update parent with current filters whenever filters change
  useEffect(() => {
    if (onFiltersUpdate) {
      const startDate = filters?.dateRange?.[0]
        ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
        : '';
      const endDate = filters?.dateRange?.[1]
        ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
        : '';

      const exportFilters = {
        search: searchValue || '',
        start_date: startDate,
        end_date: endDate,
        sort_by: sortOrder?.key || '',
        sort_order: sortOrder?.value || '',
      };

      onFiltersUpdate(exportFilters);
    }
  }, [filters, searchValue, sortOrder]); // Removed onFiltersUpdate from dependencies

  return (
    <>
      <Box className="report-main-container">
        <FilterCollapse
          fields={filterFields}
          onApply={handleApplyFilters}
          buttonText="Apply Filters"
          initialValues={filters}
        />

        <Box className="report-table-container">
          {loader ? (
            <ContentLoader />
          ) : (
            <CommonTable
              columns={columns}
              data={activityList}
              pageSize={rowsPerPage}
              currentPage={page}
              totalCount={totalCount}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              actionMenuItems={menuItems}
              onSort={handleSort}
              sortOrder={sortOrder}
            />
          )}
        </Box>
      </Box>
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
          setUserAgentValue('');
        }}
        title="User Agent"
        className="dialog-box-container"
        content={
          <Box>
            <Typography>{userAgentValue?.userAgent}</Typography>
          </Box>
        }
      />
    </>
  );
}
