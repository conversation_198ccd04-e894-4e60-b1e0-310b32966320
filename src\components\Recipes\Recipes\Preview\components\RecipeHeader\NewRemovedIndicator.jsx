import React from 'react';
import { Tooltip, Typography } from '@mui/material';
import './NewRemovedIndicator.scss';

const NewRemovedIndicator = ({ type, className = '' }) => {
  const getIndicatorClass = () => {
    switch (type) {
      case 'new':
        return 'new-removed-indicator--new';
      case 'removed':
        return 'new-removed-indicator--removed';
      default:
        return '';
    }
  };

  const getIndicatorText = () => {
    switch (type) {
      case 'new':
        return 'N';
      case 'removed':
        return 'R';
      default:
        return '';
    }
  };

  return (
    <span
      className={`new-removed-indicator ${getIndicatorClass()} ${className}`}
    >
      <Tooltip
        title={<Typography>{type === 'new' ? 'New' : 'Removed'}</Typography>}
        arrow
        classes={{ tooltip: 'info-tooltip-container ' }}
      >
        {getIndicatorText()}
      </Tooltip>
    </span>
  );
};

export default NewRemovedIndicator;
