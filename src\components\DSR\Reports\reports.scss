.dsr-reports-section {
  // background-color: var(--color-white);
  // padding: var(--spacing-xxl);
  // border-radius: var(--border-radius-lg);
  // box-shadow: var(--box-shadow-xs);
  // position: relative;
  // min-height: calc(100vh - 130px - var(--banner-height));
  overflow: auto;
  height: 100%;
  @media (max-width: 1200px) {
    .folder-desc-divider {
      margin: 16px 0 !important;
    }
  }

  .dsr-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: var(--normal-sec-border);
    .MuiTabs-root {
      border-bottom: 0 !important;
    }
    @media (max-width: 768px) {
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;
      border-bottom: 0;
    }
  }

  .DSR-table {
    position: relative;

    .dsr-table-container {
      margin-left: 190px;
      //   margin-right: 120px;
      width: auto !important;
      box-shadow: none;

      .dsr-table {
        .dsr-table-head {
          p {
            font-weight: var(--font-weight-semibold);
          }

          .dsr-cell {
            padding: 10px 5px;
            background-color: var(--color-secondary);
            font-weight: var(--font-weight-semibold);
          }

          .amount-field p {
            font-weight: var(--font-weight-medium) !important;
          }

          .branch-name {
            border-bottom: var(--normal-sec-border);
            padding-bottom: 10px;
          }

          .total-amounts {
            padding-top: 10px;
          }

          .date-fixed div,
          .total-day-fixed div {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
          }
        }

        .dsr-cell {
          padding-left: 5px;
          padding-right: 5px;
          text-align: center;

          p {
            font-family: var(--font-family-primary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-regular);
            line-height: var(--line-height-base);
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }
        }

        .date-fixed {
          position: absolute;
          min-width: 150px;
          left: 0px;
          top: auto;
          padding-left: 12px !important;
          background-color: var(--color-secondary);
          border-right: var(--normal-sec-border);
        }

        .total-day {
          background-color: var(--color-secondary);
        }

        .total-date-extra {
          height: 83.5px;
        }

        .total-branch-extra {
          height: 83.5px;
        }

        .total-day-fixed {
          position: absolute;
          min-width: 120px;
          right: 0px;
          top: auto;
          background-color: var(--color-secondary);
          border-left: var(--table-border);
        }
        .branch-cell {
          min-width: 100px;
        }

        .amount-cell {
          min-width: 120px;
        }

        .submited-by-cell {
          min-width: 200px;
        }

        .status-cell {
          min-width: 100px;
        }

        .total-id {
          height: calc(21px + 16px + 16px);
        }

        .total-date {
          height: calc(21px + 16px + 16px);
        }

        .total-actions {
          height: calc(21px + 16px + 16px);
        }
      }
    }
  }

  .DSR-table {
    position: relative;
  }

  .logbook-table-container {
    // position: relative;
    margin-left: 189px;
    margin-right: 0;
    width: auto !important;
    box-shadow: none;
    .table-cell {
      border: var(--table-border);
    }

    .heading-cell {
      padding: 6px;
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-semibold);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    }
    .fixed-header-cell {
      position: absolute;
      min-width: 190px;
      left: 0px;
      // top: auto;
      top: 0px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-top: 0 !important;
      min-height: 30px;
      border-bottom: 0 !important;
    }
    .fixed-header-cell-children {
      min-height: 71px;
    }
    .fixed-value {
      position: absolute;
      min-width: 150px;
      left: 0px;
      top: auto;
      font-weight: var(--font-weight-semibold);
    }
    .firstValue-cell {
      border-left: 0 !important;
    }

    .group-table-row {
      display: flex;
      margin-top: 8px;

      .table-cell:first-child {
        border-left: 0;
      }

      .table-cell:last-child {
        border-right: 0;
      }

      .heading-cell {
        border-bottom: 0;
        border-right: 0;
      }
    }

    .table-head {
      padding: 6px 0 0;
      font-weight: var(--font-weight-semibold);
      border: var(--table-border);
      // background-color: var(--color-dark-lavender);
      background-color: var(--color-primary);
      .title-text {
        color: var(--color-white);
      }
    }

    .table-value {
      padding: 7px 2px;
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
    }
    .total-row {
      .table-value {
        background-color: var(--color-light-gray) !important;
        span {
          font-weight: var(--font-weight-medium);
        }
      }
      .fixed-value.table-value {
        span {
          font-weight: var(--font-weight-semibold);
        }
      }
      .one-cell {
        span {
          font-weight: var(--font-weight-medium);
          width: 100%;
        }
      }
    }
    .MuiTableRow-root {
      &:nth-of-type(even) {
        background-color: var(--color-primary-opacity);
        .fixed-value:not(.dsr-table-head .fixed-value) {
          background-color: var(--color-primary-opacity);
        }
      }

      &:hover {
        background-color: var(--color-ice-blue);

        // Apply hover color to fixed cells
        .fixed-value:not(.dsr-table-head .fixed-value),
        .date-fixed:not(.dsr-table-head .date-fixed),
        .total-day-fixed:not(.dsr-table-head .total-day-fixed) {
          background-color: var(--color-ice-blue);
        }
      }
    }
  }
}
