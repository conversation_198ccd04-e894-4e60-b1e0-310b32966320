'use client';

import React, { useContext, useState, useEffect, useRef } from 'react';
import { Box, Popover, Typography } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import GeneralPage from '@/components/DSR/Reports/Generals/index';
import DSRMSRPage from '@/components/DSR/Reports/DSR';
import CustomButton from '@/components/UI/CustomButton';
import ViewWeekOutlinedIcon from '@mui/icons-material/ViewWeekOutlined';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import FilterListIcon from '@mui/icons-material/FilterList';
import RightDrawer from '@/components/UI/RightDrawer';
import GroupingReorder from '@/components/DSR/Reports/Generals/GroupingReorder/index';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import StoredFilter from '@/components/DSR/Reports/Generals/StoredFilter';
import axiosInstance from '@/helper/axios/axiosInstance';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { URLS } from '@/helper/constants/urls';
import CustomTextField from '@/components/UI/CustomTextField';
import moment from 'moment';
// import _ from 'lodash';
import { identifiers } from '@/helper/constants/identifier';
import CustomTabs from '@/components/UI/CustomTabs';
import ExportStatusIndicator from '@/components/Users/<USER>/DownloadField/components/ExportProgress';
import './reports.scss';

export default function DSRView() {
  const { authState, setIsDrawer } = useContext(AuthContext);

  const [tab, setTab] = useState(1);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [openSaveDrawer, setOpenSaveDrawer] = useState(false);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const isBM =
    authState?.web_user_active_role_id === 7 ||
    authState?.web_user_active_role_id === 14;
  const open = Boolean(anchorEl);

  const id = open ? 'simple-popper' : undefined;

  const reports_tabs = [
    { id: 1, name: 'General' },
    { id: 2, name: 'DSR/WSR' },
  ];
  const getYearMonths = () => {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const endOfYear = new Date(now.getFullYear(), 11, 31);

    return {
      startMonth: startOfYear,
      endMonth: endOfYear,
    };
  };
  /// General Tabs states
  const [dsrCatDataGEN, setDsrCatDataGEN] = useState([]);
  const [branchListGEN, setBranchListGEN] = useState([]);
  const [selectedBranchesGEN, setSelectedBranchesGEN] = useState([]);
  const [loaderGEN, setLoaderGEN] = useState(true);
  const [selectedOptionGEN, setSelectedOptionGEN] = useState('none');
  const [customMonthGEN, setCustomMonthGEN] = useState(getYearMonths);
  const [selectedMainCategoriesGEN, setSelectedMainCategoriesGEN] = useState(
    []
  );
  const [selectedSubcategoriesGEN, setSelectedSubcategoriesGEN] = useState([]);
  const [dateCustSelOptionGEN, setDateCustSelOptionGEN] =
    useState('current_month');
  const [dateCustomFilterListGEN, setDateCustFilterGENList] = useState([]);
  const [TimePeriodListGEN, setTimePeriodListGEN] = useState([]);
  const [ReportListGEN, setReportListGEN] = useState([]);
  const [ReportBYIDGen, setReportBYIDGen] = useState();
  const [ActiveCateGEN, setActiveCateGEN] = useState(false);
  const [GeneralData, setGeneralData] = useState();
  const [GenGroupData, seGenGroupData] = useState();
  const [ReportNameGEN, setReportNameGEN] = useState('');
  const [AppliedFilterGEN, setAppliedFilterGEN] = useState({
    dateCustSelOptionGEN: 'current_month',
    customMonthGEN: getYearMonths(),
    selectedBranches: [],
    selectedMainCategories: [],
    selectedSubcategories: [],
    selectedOption: '',
  });
  // DSR/MSR Tabs states
  const [dsrCatData, setDsrCatData] = useState([]);
  const [branchList, setBranchList] = useState([]);
  const [ReportBYIDDSR, setReportBYIDDSR] = useState();
  const [selectedBranches, setSelectedBranches] = useState([]);
  const [loader, setLoader] = useState(true);
  const [selectedOption, setSelectedOption] = useState('none');
  const [customStartDate, setCustomStartDate] = useState(null);
  const [customEndDate, setCustomEndDate] = useState(null);
  const [selectedMainCategories, setSelectedMainCategories] = useState([]);
  const [selectedSubcategories, setSelectedSubcategories] = useState([]);
  const [dateSelectedOption, setDateSelectedOption] = useState('today');
  const [dateFilterList, setDateFilterList] = useState([]);
  const [TimePeriodList, setTimePeriodList] = useState([]);
  const [ActiveCate, setActiveCate] = useState(false);
  const [dsrWsrData, setDsrWsrData] = useState();
  const [dsrWsrGroupData, setDsrWsrGroupData] = useState();
  const [ReportName, setReportName] = useState('');
  const [ReportList, setReportList] = useState([]);
  const [AppliedFilter, setAppliedFilter] = useState({
    dateSelectedOption: 'today',
    customStartDate: '',
    customEndDate: '',
    selectedBranches: [],
    selectedMainCategories: [],
    selectedSubcategories: [],
    selectedOption: '',
  });

  // Export status management
  const [exportStatus, setExportStatus] = useState('idle'); // 'idle', 'processing', 'completed', 'error'
  const [exportProgress, setExportProgress] = useState(0);
  const [exportFileName, setExportFileName] = useState('');
  const [exportError, setExportError] = useState('');
  const [downloadUrl, setDownloadUrl] = useState('');
  const [currentExportFormat, setCurrentExportFormat] = useState('');
  const [currentExportType, setCurrentExportType] = useState(''); // 'general' or 'dsr'

  // Ref to store progress interval for cleanup
  const progressIntervalRef = useRef(null);

  // Cleanup progress interval on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    };
  }, []);

  const onChangeGroupName = () => {
    setOpenDrawer(true);
    setIsDrawer(true);
  };
  const handleCloseDrawer = () => {
    setOpenDrawer(false);
    setIsDrawer(false);
  };
  const handleOpenFilterDrawer = () => {
    setOpenFilterDrawer(true);
    setIsDrawer(true);
  };
  const handleCloseFIlterDrawer = () => {
    setOpenFilterDrawer(false);
    setIsDrawer(false);
  };
  const onChangeSave = () => {
    setOpenSaveDrawer(true);
    setIsDrawer(true);
  };
  const handleSaveCloseDrawer = () => {
    setOpenSaveDrawer(false);
    setIsDrawer(false);
  };
  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleTabChange = (newValue) => {
    // const selectedTab = reports_tabs[newValue];

    setTab(newValue);
  };

  // Export status handlers
  const handleExportStatusDownload = () => {
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', exportFileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportDismiss = () => {
    // Clear any active progress interval
    if (progressIntervalRef.current) {
      clearTimeout(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }

    setExportStatus('idle');
    setExportProgress(0);
    setExportFileName('');
    setExportError('');
    setDownloadUrl('');
    setCurrentExportFormat('');
    setCurrentExportType('');
  };

  const handleExportRetry = () => {
    if (currentExportFormat && currentExportType) {
      if (currentExportType === 'general') {
        const filterData = {
          dateCustSelOptionGEN,
          customMonthGEN,
          selectedBranchesGEN,
          selectedMainCategoriesGEN,
          selectedSubcategoriesGEN,
          selectedOptionGEN,
        };
        handleExportWithProgress(
          () =>
            getExportGeneralReport(
              filterData,
              GeneralData?.columns_group,
              currentExportFormat
            ),
          currentExportFormat,
          'general'
        );
      } else if (currentExportType === 'dsr') {
        const filterData = {
          dateSelectedOption,
          customStartDate,
          customEndDate,
          selectedBranches,
          selectedMainCategories,
          selectedSubcategories,
          selectedOption,
        };
        handleExportWithProgress(
          () =>
            getExportDSRReport(
              filterData,
              dsrWsrData?.columns_group,
              currentExportFormat
            ),
          currentExportFormat,
          'dsr'
        );
      }
    }
  };

  // Enhanced export function with progress tracking
  const handleExportWithProgress = async (exportFunction, format, type) => {
    try {
      // Clear any existing progress interval
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      // Set initial export status
      setExportStatus('processing');
      setExportProgress(0);
      setCurrentExportFormat(format);
      setCurrentExportType(type);
      setExportError('');
      handleClose();

      // Generate filename based on type
      const filename =
        type === 'general'
          ? `${identifiers?.APP_NAME}_General_Logbook.${format === 'excel' ? 'xlsx' : format}`
          : `${identifiers?.APP_NAME}_DSR_WSR_Logbook.${format === 'excel' ? 'xlsx' : format}`;

      setExportFileName(filename);

      // Auto-manage progress with realistic simulation
      const progressSteps = [
        { progress: 15, delay: 200 },
        { progress: 35, delay: 500 },
        { progress: 55, delay: 800 },
        { progress: 75, delay: 600 },
      ];

      // Start progress simulation
      let stepIndex = 0;
      const updateProgress = () => {
        if (stepIndex < progressSteps.length) {
          const step = progressSteps[stepIndex];
          setExportProgress(step.progress);
          stepIndex++;

          progressIntervalRef.current = setTimeout(updateProgress, step.delay);
        }
      };

      // Start progress updates
      updateProgress();

      // Execute the export function
      await exportFunction();

      // Clear any remaining progress intervals
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      // Complete the progress
      setExportProgress(100);
      setExportStatus('completed');
    } catch (error) {
      // Clear progress interval on error
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      console.error('Export error:', error);
      setExportStatus('error');
      setExportError(error?.message || 'Export failed');
      // setApiMessage('error', error?.message || 'Export failed');
    }
  };

  const FindParentID = (data, targetIds) => {
    const parentIds = data
      .filter((item) =>
        item?.payment_type_category.some((category) =>
          targetIds.includes(category?.payment_type_category_id)
        )
      )
      .map((item) => item?.id);
    return parentIds;
  };
  // Report By ID
  const getReportByID = async (ID) => {
    setLoader(true);
    setLoaderGEN(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_REPORT_BY_ID}/${ID}`
      );
      if (status === 200) {
        const filter = data?.data?.filter_value;
        const column = data?.data?.group_value;

        if (tab === 1 || tab === '1') {
          const CatstringArray =
            filter?.dsr_payment_type_category &&
            filter?.dsr_payment_type_category?.split(',');
          const CatenumberArray =
            CatstringArray &&
            CatstringArray?.length > 0 &&
            CatstringArray.map(Number);
          const parentId =
            CatenumberArray &&
            CatenumberArray?.length > 0 &&
            FindParentID(dsrCatDataGEN, CatenumberArray);
          const BranchstringArray =
            filter?.branch_id && filter?.branch_id?.split(',');
          const BranchnumberArray =
            BranchstringArray &&
            BranchstringArray?.length > 0 &&
            BranchstringArray.map(Number);
          filter?.date_filter && setDateCustSelOptionGEN(filter?.date_filter);
          filter?.start_date && filter?.end_date
            ? setCustomMonthGEN({
                startMonth: filter?.start_date,
                endMonth: filter?.end_date,
              })
            : setCustomMonthGEN(getYearMonths);
          BranchnumberArray && BranchnumberArray?.length > 0
            ? setSelectedBranchesGEN(BranchnumberArray)
            : setSelectedBranchesGEN([]);
          parentId && parentId?.length > 0
            ? setSelectedMainCategoriesGEN(parentId)
            : setSelectedMainCategoriesGEN([]);
          CatenumberArray && CatenumberArray?.length > 0
            ? setSelectedSubcategoriesGEN(CatenumberArray)
            : setSelectedSubcategoriesGEN([]);
          setSelectedOptionGEN(
            filter?.time_period ? filter?.time_period : 'none'
          );
          const dsrfilterData = {
            dateCustSelOptionGEN: filter?.date_filter
              ? filter?.date_filter
              : 'current_month',
            customMonthGEN: {
              startMonth: filter?.start_date ? filter?.start_date : null,
              endMonth: filter?.end_date ? filter?.end_date : null,
            },
            selectedBranches:
              BranchnumberArray && BranchnumberArray?.length > 0
                ? BranchnumberArray
                : [],
            selectedMainCategories:
              parentId && parentId?.length > 0 ? parentId : [],
            selectedSubcategories:
              CatenumberArray && CatenumberArray?.length > 0
                ? CatenumberArray
                : [],
            selectedOption: filter?.time_period ? filter?.time_period : 'none',
            isGroup: true,
          };
          getGeneralReportFilter(dsrfilterData, column);
          setAppliedFilterGEN(dsrfilterData);
          setReportBYIDGen(data?.data);
          data?.data?.filter_name
            ? setReportNameGEN(data?.data?.filter_name)
            : setReportNameGEN('');
          setActiveCateGEN(filter?.categotyActiveStatus ? true : false);
          getCategoriesPaymentListGen(
            filter?.categotyActiveStatus ? true : false,
            filter?.date_filter ? filter?.date_filter : 'current_month',
            parentId,
            CatenumberArray,
            isBM ? authState?.branch?.id : ''
          );
        } else {
          const CatstringArray =
            filter?.dsr_payment_type_category &&
            filter?.dsr_payment_type_category?.split(',');
          const CatenumberArray =
            CatstringArray &&
            CatstringArray?.length > 0 &&
            CatstringArray.map(Number);
          const parentId =
            CatenumberArray && FindParentID(dsrCatData, CatenumberArray);
          const BranchstringArray =
            filter?.branch_id && filter?.branch_id?.split(',');
          const BranchnumberArray =
            BranchstringArray &&
            BranchstringArray?.length > 0 &&
            BranchstringArray.map(Number);
          filter?.date_filter
            ? setDateSelectedOption(filter?.date_filter)
            : setDateSelectedOption('today');
          filter?.start_date
            ? setCustomStartDate(filter?.start_date)
            : setCustomStartDate(null);
          filter?.end_date
            ? setCustomEndDate(filter?.end_date)
            : setCustomEndDate(null);
          BranchnumberArray && BranchnumberArray?.length > 0
            ? setSelectedBranches(BranchnumberArray)
            : setSelectedBranches([]);
          parentId && parentId?.length > 0
            ? setSelectedMainCategories(parentId)
            : setSelectedMainCategories([]);
          CatenumberArray && CatenumberArray?.length > 0
            ? setSelectedSubcategories(CatenumberArray)
            : setSelectedSubcategories([]);
          setSelectedOption(filter?.time_period ? filter?.time_period : 'none');
          const dsrfilterData = {
            dateSelectedOption: filter?.date_filter
              ? filter?.date_filter
              : 'today',
            customStartDate: filter?.start_date,
            customEndDate: filter?.end_date,
            selectedBranches:
              BranchnumberArray && BranchnumberArray?.length > 0
                ? BranchnumberArray
                : [],
            selectedMainCategories:
              parentId && parentId?.length > 0 ? parentId : [],
            selectedSubcategories:
              CatenumberArray && CatenumberArray?.length > 0
                ? CatenumberArray
                : [],
            selectedOption: filter?.time_period ? filter?.time_period : 'none',
            isGroup: true,
          };
          setAppliedFilter(dsrfilterData);
          getDSRReportFilter(dsrfilterData, column);
          setReportBYIDDSR(data?.data);
          data?.data?.filter_name
            ? setReportName(data?.data?.filter_name)
            : setReportName('');
          setActiveCate(filter?.categotyActiveStatus);
          getCategoriesPaymentList(
            filter?.categotyActiveStatus ? true : false,
            filter?.date_filter ? filter?.date_filter : 'today',
            parentId,
            CatenumberArray,
            isBM ? authState?.branch?.id : ''
          );
        }
        setLoader(false);
        setLoaderGEN(false);
      }
    } catch (error) {
      setLoader(false);
      setLoaderGEN(false);
      // setReportBYID([]);
      setReportBYIDDSR([]);
      setReportBYIDGen([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Fetches the list of All Reports By ID
  const getReportsList = async (userId, type, IsDelete, DeletedID) => {
    // setLoader(true);
    // setLoaderGEN(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.REPORT_FILTER_BY_USER_ID}/${userId}?user_filter_type=${type}`
      );
      if (status === 200) {
        if (type === 'day') {
          data?.data && data?.data?.length > 0
            ? setReportList(data?.data)
            : setReportList([]);
          if (IsDelete && DeletedID === ReportBYIDDSR?.id) {
            const dsrfilterData = {
              dateSelectedOption: 'today',
              customStartDate: '',
              customEndDate: '',
              selectedBranches: [],
              selectedMainCategories: [],
              selectedSubcategories: [],
              selectedOption: '',
            };
            getDSRReportFilter(dsrfilterData);
            setDateSelectedOption('today');
            setCustomStartDate(null);
            setCustomEndDate(null);
            setSelectedBranches([]);
            setSelectedMainCategories([]);
            setSelectedSubcategories([]);
            setSelectedOption('none');
            // setReportBYID();
            setReportBYIDDSR();
          }
        } else {
          data?.data && data?.data?.length > 0
            ? setReportListGEN(data?.data)
            : setReportListGEN([]);

          if (IsDelete && DeletedID === ReportBYIDGen?.id) {
            const filterData = {
              dateCustSelOptionGEN: 'current_month',
              customMonthGEN: '',
              selectedBranches: [],
              selectedMainCategories: [],
              selectedSubcategories: [],
              selectedOption: '',
            };
            getGeneralReportFilter(filterData);
            setDateCustSelOptionGEN('current_month');
            setCustomMonthGEN({
              startMonth: null,
              endMonth: null,
            });
            setSelectedBranchesGEN([]);
            setSelectedMainCategoriesGEN([]);
            setSelectedSubcategoriesGEN([]);
            setSelectedOptionGEN('none');
            // setReportBYID();
            setReportBYIDGen();
            setActiveCateGEN(false);
          }
        }
        // setLoader(false);
        // setLoaderGEN(false);
      }
    } catch (error) {
      // setLoader(false);
      // setLoaderGEN(false);
      setReportList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Fetches the list of date filter For Day
  const getDateFilterListDay = async () => {
    setLoader(true);

    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.DATE_FILTER_LIST}?report_filter_type=day`
      );
      if (status === 200) {
        setLoader(false);
        const filterList = data?.data?.map((user) => ({
          label: user?.value,
          value: user?.key,
        }));
        setDateFilterList(filterList);
      }
    } catch (error) {
      setLoader(false);
      setDateFilterList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Fetches the list of date filter For General
  const getDateCustomFilterGeneral = async () => {
    setLoaderGEN(true);

    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.DATE_FILTER_LIST}?report_filter_type=general`
      );
      if (status === 200) {
        setLoaderGEN(false);
        const filterList = data?.data?.map((user) => ({
          label: user?.value,
          value: user?.key,
        }));
        setDateCustFilterGENList(filterList);
      }
    } catch (error) {
      setLoaderGEN(false);
      setDateCustFilterGENList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Fetches the list of Time period filter For DSR/WSR
  const getTimePeriodFilter = async () => {
    setLoader(true);

    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.DATE_FILTER_LIST}?report_filter_type=timeperiod`
      );
      if (status === 200) {
        setLoader(false);
        const filterList = data?.data?.map((user) => ({
          label: user?.value,
          value: user?.key,
        }));
        setTimePeriodList(filterList);
        setTimePeriodListGEN(filterList);
      }
    } catch (error) {
      setLoader(false);
      setTimePeriodList([]);
      setTimePeriodListGEN([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getDSRRequestData = (filterData, Columns) => {
    const sendData = {
      branch_id:
        filterData?.selectedBranches && filterData?.selectedBranches?.length > 0
          ? filterData?.selectedBranches?.toString()
          : '',
      dsr_payment_type_category:
        filterData?.selectedSubcategories &&
        filterData?.selectedSubcategories?.length > 0
          ? filterData?.selectedSubcategories?.toString()
          : '',
      date_filter: filterData?.dateSelectedOption
        ? filterData?.dateSelectedOption
        : 'today',
      time_period:
        filterData?.selectedOption && filterData?.selectedOption !== 'none'
          ? filterData?.selectedOption
          : '',
      end_date: filterData?.customEndDate ? filterData?.customEndDate : '',
      start_date: filterData?.customStartDate
        ? filterData?.customStartDate
        : '',
      report_type: 'day',
      ...(Columns && { columns_group: Columns }),
    };
    return sendData;
  };
  // Fetches DSR/WSR filter report
  const getDSRReportFilter = async (filterData, Columns) => {
    setLoader(true);

    try {
      const { status, data } = await axiosInstance.post(
        `${URLS.GET_DSR_REPORT_FILTER}`,
        getDSRRequestData(filterData, Columns)
      );
      if (status === 200) {
        setLoader(false);
        const dsr =
          data?.data?.total && data?.data?.data?.length > 0
            ? {
                ...data.data,
                data: [...(data?.data?.data || []), data?.data?.total],
              }
            : { ...data.data };
        setDsrWsrData(dsr);

        setDsrWsrGroupData(data?.data);
      }
    } catch (error) {
      setLoader(false);
      setDsrWsrData();
      setDsrWsrGroupData();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getGenRequestData = (filterData, Columns) => {
    const sendData = {
      branch_id:
        filterData?.selectedBranches && filterData?.selectedBranches?.length > 0
          ? filterData?.selectedBranches?.toString()
          : '',
      dsr_payment_type_category:
        filterData?.selectedSubcategories &&
        filterData?.selectedSubcategories?.length > 0
          ? filterData?.selectedSubcategories?.toString()
          : '',
      date_filter: filterData?.dateCustSelOptionGEN
        ? filterData?.dateCustSelOptionGEN
        : 'current_month',
      time_period:
        filterData?.selectedOption && filterData?.selectedOption !== 'none'
          ? filterData?.selectedOption
          : '',
      start_date:
        filterData?.dateCustSelOptionGEN === 'custom' &&
        filterData?.customMonthGEN &&
        filterData?.customMonthGEN?.startMonth
          ? moment(filterData?.customMonthGEN?.startMonth)
              .startOf('month')
              .format('YYYY-MM-DD')
          : '',
      end_date:
        filterData?.dateCustSelOptionGEN === 'custom' &&
        filterData?.customMonthGEN &&
        filterData?.customMonthGEN?.endMonth
          ? moment(filterData?.customMonthGEN?.endMonth)
              .endOf('month')
              .format('YYYY-MM-DD')
          : '',
      report_type: 'general',
      ...(Columns && { columns_group: Columns }),
    };
    return sendData;
  };
  // Fetches General filter report
  const getGeneralReportFilter = async (filterData, Columns) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.post(
        `${URLS.GET_DSR_REPORT_FILTER}`,
        getGenRequestData(filterData, Columns)
      );
      if (status === 200) {
        const dsr =
          data?.data?.total && data?.data?.data?.length > 0
            ? {
                ...data.data,
                data: [...(data?.data?.data || []), data?.data?.total],
              }
            : { ...data.data };
        setGeneralData(dsr);
        seGenGroupData(data?.data);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setGeneralData();
      seGenGroupData();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getDSRRequestDataExport = (filterData, Columns, type) => {
    const sendData = {
      branch_id:
        filterData?.selectedBranches && filterData?.selectedBranches?.length > 0
          ? filterData?.selectedBranches?.toString()
          : '',
      dsr_payment_type_category:
        filterData?.selectedSubcategories &&
        filterData?.selectedSubcategories?.length > 0
          ? filterData?.selectedSubcategories?.toString()
          : '',
      date_filter: filterData?.dateSelectedOption
        ? filterData?.dateSelectedOption
        : 'today',
      time_period:
        filterData?.selectedOption && filterData?.selectedOption !== 'none'
          ? filterData?.selectedOption
          : '',
      end_date: filterData?.customEndDate ? filterData?.customEndDate : '',
      start_date: filterData?.customStartDate
        ? filterData?.customStartDate
        : '',
      report_type: 'day',
      file_type: type,
      ...(Columns && { columns_group: Columns }),
    };
    return sendData;
  };
  const getGenRequestDataExport = (filterData, Columns, type) => {
    const sendData = {
      branch_id:
        filterData?.selectedBranchesGEN &&
        filterData?.selectedBranchesGEN?.length > 0
          ? filterData?.selectedBranchesGEN?.toString()
          : '',
      dsr_payment_type_category:
        filterData?.selectedSubcategoriesGEN &&
        filterData?.selectedSubcategoriesGEN?.length > 0
          ? filterData?.selectedSubcategoriesGEN?.toString()
          : '',
      date_filter: filterData?.dateCustSelOptionGEN
        ? filterData?.dateCustSelOptionGEN
        : 'current_month',
      time_period:
        filterData?.selectedOptionGEN &&
        filterData?.selectedOptionGEN !== 'none'
          ? filterData?.selectedOptionGEN
          : '',
      start_date:
        filterData?.dateCustSelOptionGEN === 'custom' &&
        filterData?.customMonthGEN &&
        filterData?.customMonthGEN?.startMonth
          ? moment(filterData?.customMonthGEN?.startMonth)
              .startOf('month')
              .format('YYYY-MM-DD')
          : '',
      end_date:
        filterData?.dateCustSelOptionGEN === 'custom' &&
        filterData?.customMonthGEN &&
        filterData?.customMonthGEN?.endMonth
          ? moment(filterData?.customMonthGEN?.endMonth)
              .endOf('month')
              .format('YYYY-MM-DD')
          : '',
      report_type: 'general',
      file_type: type,
      ...(Columns && { columns_group: Columns }),
    };
    return sendData;
  };
  // Export General Report
  const getExportGeneralReport = async (filterData, Columns, type) => {
    try {
      const { status, data } = await axiosInstance.post(
        `${URLS.EXPORT_REPORT}`,
        getGenRequestDataExport(filterData, Columns, type),
        {
          responseType: 'blob',
        }
      );
      if (status === 200) {
        const url = window.URL.createObjectURL(new Blob([data]));
        var filename;
        filename =
          type === 'pdf'
            ? `${identifiers?.APP_NAME}_General_Logbook.pdf`
            : type === 'csv'
              ? `${identifiers?.APP_NAME}_General_Logbook.csv`
              : `${identifiers?.APP_NAME}_General_Logbook.xlsx`;

        // Store download URL for the progress indicator
        setDownloadUrl(url);
        setExportFileName(filename);
      }
    } catch (error) {
      throw error; // Re-throw to be caught by handleExportWithProgress
    }
  };
  // Export DSR Reports
  const getExportDSRReport = async (filterData, Columns, type) => {
    try {
      const { status, data } = await axiosInstance.post(
        `${URLS.EXPORT_REPORT}`,
        getDSRRequestDataExport(filterData, Columns, type),
        {
          responseType: 'blob',
        }
      );
      if (status === 200) {
        const url = window.URL.createObjectURL(new Blob([data]));
        var filename;
        filename =
          type === 'pdf'
            ? `${identifiers?.APP_NAME}_DSR_WSR_Logbook.pdf`
            : type === 'csv'
              ? `${identifiers?.APP_NAME}_DSR_WSR_Logbook.csv`
              : `${identifiers?.APP_NAME}_DSR_WSR_Logbook.xlsx`;

        // Store download URL for the progress indicator
        setDownloadUrl(url);
        setExportFileName(filename);
      }
    } catch (error) {
      throw error; // Re-throw to be caught by handleExportWithProgress
    }
  };
  // Fetches the list of branches
  const getBranchList = async () => {
    // setLoader(true);
    // setLoaderGEN(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_BRANCH_LIST}?search=&page=1&size=&branchStatus=active`
      );
      if (status === 200) {
        // setLoader(false);
        // setLoaderGEN(false);
        const filterUserList = data?.data?.map((user) => ({
          label: user?.branch_name,
          value: user?.id,
          color: user?.branch_color,
        }));
        setBranchList(filterUserList);
        setBranchListGEN(filterUserList);
      }
    } catch (error) {
      // setLoader(false);
      // setLoaderGEN(false);
      setBranchList([]);
      setBranchListGEN([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Fetches the categories and payment type list
  const getCategoriesPaymentList = async (
    cateActive,
    custom,
    parent,
    subCat,
    branch_id
  ) => {
    // setLoader(true);

    parent && parent?.length > 0
      ? setSelectedMainCategories(parent)
      : setSelectedMainCategories([]);
    subCat && subCat?.length > 0
      ? setSelectedSubcategories(subCat)
      : setSelectedSubcategories([]);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FILTER_CATEGORY_LIST}?include_inactive=${cateActive}&report_filter_type=dsr&branch_id=${branch_id ? branch_id : ''}`
      );
      if (status === 200) {
        // setLoader(false);
        const catmodified = data?.data?.map((paymentType) => ({
          ...paymentType,
          catList:
            paymentType?.payment_type_category?.map((category) => ({
              ...category,
              id: category?.payment_type_category_id,
              catList:
                category?.categoryBranchValue?.map((branchValue) => ({
                  ...branchValue,
                  id: branchValue?.payment_type_category_branch_id,
                })) || [],
            })) || [],
        }));
        setDsrCatData(catmodified);
      }
    } catch (error) {
      // setLoader(false);
      setDsrCatData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Fetches the categories and payment type list
  const getCategoriesPaymentListGen = async (
    cateActive,
    custom,
    parent,
    subCat,
    branch_id
  ) => {
    // setLoaderGEN(true);
    parent && parent?.length > 0
      ? setSelectedMainCategoriesGEN(parent)
      : setSelectedMainCategoriesGEN([]);
    subCat && subCat?.length > 0
      ? setSelectedSubcategoriesGEN(subCat)
      : setSelectedSubcategoriesGEN([]);
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.GET_FILTER_CATEGORY_LIST}?include_inactive=${cateActive}&report_filter_type=general&branch_id=${branch_id ? branch_id : ''}`
      );
      if (status === 200) {
        // setLoaderGEN(false);
        const catmodified = data?.data?.map((paymentType) => ({
          ...paymentType,
          catList:
            paymentType?.payment_type_category?.map((category) => ({
              ...category,
              id: category?.payment_type_category_id,
              catList:
                category?.categoryBranchValue?.map((branchValue) => ({
                  ...branchValue,
                  id: branchValue?.payment_type_category_branch_id,
                })) || [],
            })) || [],
        }));

        setDsrCatDataGEN(catmodified);
      }
    } catch (error) {
      // setLoaderGEN(false);
      setDsrCatDataGEN([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getDSRRequestDataSave = (filterData) => {
    const sendData = {
      branch_id:
        filterData?.selectedBranches && filterData?.selectedBranches?.length > 0
          ? filterData?.selectedBranches?.toString()
          : '',
      dsr_payment_type_category:
        filterData?.selectedSubcategories &&
        filterData?.selectedSubcategories?.length > 0
          ? filterData?.selectedSubcategories?.toString()
          : '',
      date_filter: filterData?.dateSelectedOption
        ? filterData?.dateSelectedOption
        : 'today',
      categotyActiveStatus: ActiveCate,
      time_period:
        filterData?.selectedOption && filterData?.selectedOption !== 'none'
          ? filterData?.selectedOption
          : '',
      end_date: customEndDate ? customEndDate : '',
      start_date: customStartDate ? customStartDate : '',
    };
    return sendData;
  };
  // Save DSR Filter
  const saveReportDSR = async (filterData, Columns, fname) => {
    setLoader(true);
    const dsrFil = getDSRRequestDataSave(filterData);
    const sendData = {
      filter_value: JSON.stringify(dsrFil),
      filter_name: fname,
      group_value: JSON.stringify(Columns),
      user_filter_type: 'day',
    };
    const ApiUrl =
      ReportBYIDDSR && ReportBYIDDSR?.filter_name
        ? URLS.UPDATE_REPORTS + ReportBYIDDSR?.id
        : URLS.SAVE_REPORTS;
    const method = ReportBYIDDSR && ReportBYIDDSR?.filter_name ? 'put' : 'post';
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, sendData);
      if (status === 200) {
        getReportsList(authState?.id, 'day');
        setApiMessage('success', data?.message);
        const dsrfilterData = {
          dateSelectedOption: 'today',
          customStartDate: '',
          customEndDate: '',
          selectedBranches: [],
          selectedMainCategories: [],
          selectedSubcategories: [],
          selectedOption: '',
        };
        getDSRReportFilter(dsrfilterData);
        setDateSelectedOption('today');
        setCustomStartDate(null);
        setCustomEndDate(null);
        setSelectedBranches([]);
        setSelectedMainCategories([]);
        setSelectedSubcategories([]);
        setSelectedOption('none');
        // setReportBYID();
        setReportBYIDDSR();
        setActiveCate(false);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getGenRequestDataSave = (filterData) => {
    const sendData = {
      branch_id:
        filterData?.selectedBranchesGEN &&
        filterData?.selectedBranchesGEN?.length > 0
          ? filterData?.selectedBranchesGEN?.toString()
          : '',
      dsr_payment_type_category:
        filterData?.selectedSubcategoriesGEN &&
        filterData?.selectedSubcategoriesGEN?.length > 0
          ? filterData?.selectedSubcategoriesGEN?.toString()
          : '',
      date_filter: filterData?.dateCustSelOptionGEN
        ? filterData?.dateCustSelOptionGEN
        : 'current_month',
      time_period:
        filterData?.selectedOptionGEN &&
        filterData?.selectedOptionGEN !== 'none'
          ? filterData?.selectedOptionGEN
          : '',
      categotyActiveStatus: ActiveCateGEN,
      start_date:
        filterData?.dateCustSelOptionGEN === 'custom' &&
        filterData?.customMonthGEN &&
        filterData?.customMonthGEN?.startMonth
          ? moment(filterData?.customMonthGEN?.startMonth)
              .startOf('month')
              .format('YYYY-MM-DD')
          : '',
      end_date:
        filterData?.dateCustSelOptionGEN === 'custom' &&
        filterData?.customMonthGEN &&
        filterData?.customMonthGEN?.endMonth
          ? moment(filterData?.customMonthGEN?.endMonth)
              .endOf('month')
              .format('YYYY-MM-DD')
          : '',
    };
    return sendData;
  };
  // Save General Filter
  const SaveReportGen = async (filterData, Columns, fname) => {
    setLoaderGEN(true);
    const generalFil = getGenRequestDataSave(filterData);
    const sendData = {
      filter_value: JSON.stringify(generalFil),
      filter_name: fname,
      group_value: JSON.stringify(Columns),
      user_filter_type: 'general',
    };
    const ApiUrl =
      ReportBYIDGen && ReportBYIDGen?.filter_name
        ? URLS.UPDATE_REPORTS + ReportBYIDGen?.id
        : URLS.SAVE_REPORTS;
    const method = ReportBYIDGen && ReportBYIDGen?.filter_name ? 'put' : 'post';
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, sendData);
      if (status === 200) {
        getReportsList(authState?.id, 'general');
        const filterData = {
          dateCustSelOptionGEN: 'current_month',
          customMonthGEN: '',
          selectedBranches: [],
          selectedMainCategories: [],
          selectedSubcategories: [],
          selectedOption: '',
        };
        getGeneralReportFilter(filterData);
        setDateCustSelOptionGEN('current_month');
        setCustomMonthGEN({
          startMonth: null,
          endMonth: null,
        });
        setSelectedBranchesGEN([]);
        setSelectedMainCategoriesGEN([]);
        setSelectedSubcategoriesGEN([]);
        setSelectedOptionGEN('none');
        // setReportBYID();
        setReportBYIDGen();
        setActiveCateGEN(false);
        setLoaderGEN(false);
        setApiMessage('success', data?.message);
      }
    } catch (error) {
      setLoaderGEN(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Triggered when component is mounted to fetch initial data
  useEffect(() => {
    getBranchList();
    getDateFilterListDay();
    getTimePeriodFilter();
    getDateCustomFilterGeneral();
    // // General report default api
    // const filterData = {
    //   dateCustSelOptionGEN: 'current_month',
    //   customMonthGEN: '',
    //   selectedBranches: [],
    //   selectedMainCategories: [],
    //   selectedSubcategories: [],
    //   selectedOption: '',
    // };
    // getGeneralReportFilter(filterData);
    // // DSR/WSR report default api
    // const dsrfilterData = {
    //   dateSelectedOption: 'today',
    //   customStartDate: '',
    //   customEndDate: '',
    //   selectedBranches: [],
    //   selectedMainCategories: [],
    //   selectedSubcategories: [],
    //   selectedOption: '',
    // };
    // getDSRReportFilter(dsrfilterData);
  }, []);
  useEffect(() => {
    if (isBM && authState?.branch?.id) {
      setSelectedBranches([authState?.branch?.id]);
      setSelectedBranchesGEN([authState?.branch?.id]);
      // General report default api
      const filterData = {
        dateCustSelOptionGEN: 'current_month',
        customMonthGEN: '',
        selectedBranches: [authState?.branch?.id],
        selectedMainCategories: [],
        selectedSubcategories: [],
        selectedOption: '',
      };
      getGeneralReportFilter(filterData);
      setAppliedFilterGEN(filterData);

      // DSR/WSR report default api
      const dsrfilterData = {
        dateSelectedOption: 'today',
        customStartDate: '',
        customEndDate: '',
        selectedBranches: [authState?.branch?.id],
        selectedMainCategories: [],
        selectedSubcategories: [],
        selectedOption: '',
      };
      getDSRReportFilter(dsrfilterData);
      setAppliedFilter(dsrfilterData);
      getCategoriesPaymentList(false, 'today', '', '', authState?.branch?.id);
      getCategoriesPaymentListGen(
        false,
        'current_month',
        '',
        '',
        authState?.branch?.id
      );
    } else if (authState?.id) {
      // General report default api
      const filterData = {
        dateCustSelOptionGEN: 'current_month',
        customMonthGEN: '',
        selectedBranches: [],
        selectedMainCategories: [],
        selectedSubcategories: [],
        selectedOption: '',
      };
      getGeneralReportFilter(filterData);
      // DSR/WSR report default api
      const dsrfilterData = {
        dateSelectedOption: 'today',
        customStartDate: '',
        customEndDate: '',
        selectedBranches: [],
        selectedMainCategories: [],
        selectedSubcategories: [],
        selectedOption: '',
      };
      getDSRReportFilter(dsrfilterData);
      getCategoriesPaymentList(false, 'today');
      getCategoriesPaymentListGen(false, 'current_month');
    }
  }, [authState?.id]);
  useEffect(() => {
    if (authState && authState?.id) {
      getReportsList(authState?.id, 'general');
      getReportsList(authState?.id, 'day');
    }
  }, [authState]);

  const formatDateForComparison = (date) => {
    if (!date) return '';
    const d = new Date(date);
    const yyyy = d.getFullYear();
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const dd = String(d.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  };

  const getCurrentContent = () => {
    switch (tab) {
      case 1:
        return (
          <>
            {ReportBYIDGen && ReportBYIDGen?.filter_name && (
              <Box>
                <Typography className="title-text pb8">
                  <span className="title-text fw600 pr4">Report's name :</span>
                  {ReportBYIDGen && ReportBYIDGen?.filter_name
                    ? ReportBYIDGen?.filter_name
                    : ''}
                </Typography>
              </Box>
            )}

            <GeneralPage
              dsrCatData={dsrCatDataGEN}
              setDsrCatData={setDsrCatDataGEN}
              branchList={branchListGEN}
              setBranchList={setBranchListGEN}
              selectedBranches={selectedBranchesGEN}
              setSelectedBranches={setSelectedBranchesGEN}
              selectedOption={selectedOptionGEN}
              setSelectedOption={setSelectedOptionGEN}
              customMonthGEN={customMonthGEN}
              setCustomMonthGEN={setCustomMonthGEN}
              selectedMainCategories={selectedMainCategoriesGEN}
              setSelectedMainCategories={setSelectedMainCategoriesGEN}
              selectedSubcategories={selectedSubcategoriesGEN}
              setSelectedSubcategories={setSelectedSubcategoriesGEN}
              TimePeriodList={TimePeriodListGEN}
              setTimePeriodList={setTimePeriodListGEN}
              setLoader={setLoaderGEN}
              loader={loaderGEN}
              dateCustomFilterListGEN={dateCustomFilterListGEN}
              dateCustSelOptionGEN={dateCustSelOptionGEN}
              setDateCustSelOptionGEN={setDateCustSelOptionGEN}
              ActiveCate={ActiveCateGEN}
              setActiveCate={setActiveCateGEN}
              getCategoriesPaymentList={getCategoriesPaymentListGen}
              GeneralData={GeneralData}
              getGeneralReportFilter={getGeneralReportFilter}
              setAppliedFilterGEN={setAppliedFilterGEN}
              AppliedFilterGEN={AppliedFilterGEN}
              isBM={isBM}
              authState={authState}
            />
          </>
        );
      case 2:
        return (
          <>
            {ReportBYIDDSR && ReportBYIDDSR?.filter_name && (
              <Box>
                <Typography className="title-text pb8">
                  <span className="title-text fw600 pr4">Report's name :</span>
                  {ReportBYIDDSR && ReportBYIDDSR?.filter_name
                    ? ReportBYIDDSR?.filter_name
                    : ''}
                </Typography>
              </Box>
            )}

            <DSRMSRPage
              dsrCatData={dsrCatData}
              setDsrCatData={setDsrCatData}
              branchList={branchList}
              setBranchList={setBranchList}
              selectedBranches={selectedBranches}
              setSelectedBranches={setSelectedBranches}
              selectedOption={selectedOption}
              setSelectedOption={setSelectedOption}
              customStartDate={customStartDate}
              setCustomStartDate={setCustomStartDate}
              customEndDate={customEndDate}
              setCustomEndDate={setCustomEndDate}
              selectedMainCategories={selectedMainCategories}
              setSelectedMainCategories={setSelectedMainCategories}
              selectedSubcategories={selectedSubcategories}
              setSelectedSubcategories={setSelectedSubcategories}
              dateSelectedOption={dateSelectedOption}
              setDateSelectedOption={setDateSelectedOption}
              dateFilterList={dateFilterList}
              setDateFilterList={setDateFilterList}
              TimePeriodList={TimePeriodList}
              setTimePeriodList={setTimePeriodList}
              ActiveCate={ActiveCate}
              setActiveCate={setActiveCate}
              setLoader={setLoader}
              loader={loader}
              getCategoriesPaymentList={getCategoriesPaymentList}
              getDSRReportFilter={getDSRReportFilter}
              dsrWsrData={dsrWsrData}
              setAppliedFilter={setAppliedFilter}
              AppliedFilter={AppliedFilter}
              isBM={isBM}
              authState={authState}
            />
          </>
        );
      default:
        return null;
    }
  };
  return (
    <>
      <Box className="dsr-reports-section">
        <Box className="dsr-tabs">
          <CustomTabs
            tabs={reports_tabs?.map((tab) => ({
              id: tab?.id,
              label: tab?.name,
            }))}
            initialTab={tab}
            onTabChange={handleTabChange}
          />

          {tab === 1 || tab === '1' ? (
            <Box className="custom-btns">
              <CustomButton
                variant="outlined"
                onClick={handleOpenFilterDrawer}
                startIcon={<FilterListIcon />}
                title={'Filters'}
                disabled={ReportListGEN && ReportListGEN?.length === 0}
              />

              <CustomButton
                variant="outlined"
                onClick={onChangeGroupName}
                startIcon={<ViewWeekOutlinedIcon />}
                title="Columns"
                disabled={GeneralData && GeneralData?.data?.length === 0}
              />
              <CustomButton
                variant="outlined"
                onClick={onChangeSave}
                startIcon={<BookmarkBorderIcon />}
                title={
                  ReportBYIDGen && ReportBYIDGen?.filter_name
                    ? 'Update'
                    : 'Save'
                }
                disabled={GeneralData && GeneralData?.data?.length === 0}
              />
              <CustomButton
                variant="contained"
                startIcon={<FileDownloadOutlinedIcon />}
                onClick={handleClick}
                title="Export"
                disabled={GeneralData && GeneralData?.data?.length === 0}
              />
              <Popover
                className="export-popover"
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
              >
                <Box className="export-option">
                  <Typography
                    className="title-text fw600 pb8 cursor-pointer"
                    onClick={() => {
                      const filterData = {
                        dateCustSelOptionGEN,
                        customMonthGEN: customMonthGEN,
                        selectedBranchesGEN,
                        selectedMainCategoriesGEN,
                        selectedSubcategoriesGEN,
                        selectedOptionGEN,
                      };

                      handleExportWithProgress(
                        () =>
                          getExportGeneralReport(
                            filterData,
                            GeneralData?.columns_group,
                            'pdf'
                          ),
                        'pdf',
                        'general'
                      );
                    }}
                  >
                    PDF
                  </Typography>
                  <Typography
                    className="title-text fw600 pb8 cursor-pointer"
                    onClick={() => {
                      const filterData = {
                        dateCustSelOptionGEN,
                        customMonthGEN: customMonthGEN,
                        selectedBranchesGEN,
                        selectedMainCategoriesGEN,
                        selectedSubcategoriesGEN,
                        selectedOptionGEN,
                      };

                      handleExportWithProgress(
                        () =>
                          getExportGeneralReport(
                            filterData,
                            GeneralData?.columns_group,
                            'excel'
                          ),
                        'excel',
                        'general'
                      );
                    }}
                  >
                    Excel
                  </Typography>
                  <Typography
                    className="title-text fw600 cursor-pointer"
                    onClick={() => {
                      const filterData = {
                        dateCustSelOptionGEN,
                        customMonthGEN: customMonthGEN,
                        selectedBranchesGEN,
                        selectedMainCategoriesGEN,
                        selectedSubcategoriesGEN,
                        selectedOptionGEN,
                      };

                      handleExportWithProgress(
                        () =>
                          getExportGeneralReport(
                            filterData,
                            GeneralData?.columns_group,
                            'csv'
                          ),
                        'csv',
                        'general'
                      );
                    }}
                  >
                    CSV
                  </Typography>
                </Box>
              </Popover>
            </Box>
          ) : (
            <Box className="custom-btns">
              <CustomButton
                variant="outlined"
                onClick={handleOpenFilterDrawer}
                startIcon={<FilterListIcon />}
                title="Filters"
                disabled={ReportList && ReportList?.length === 0}
              />

              <CustomButton
                variant="outlined"
                onClick={onChangeGroupName}
                startIcon={<ViewWeekOutlinedIcon />}
                title="Columns"
                disabled={dsrWsrData && dsrWsrData?.data?.length === 0}
              />
              <CustomButton
                variant="outlined"
                onClick={onChangeSave}
                startIcon={<BookmarkBorderIcon />}
                title={
                  ReportBYIDDSR && ReportBYIDDSR?.filter_name
                    ? 'Update'
                    : 'Save'
                }
                disabled={dsrWsrData && dsrWsrData?.data?.length === 0}
              />
              <CustomButton
                variant="contained"
                startIcon={<FileDownloadOutlinedIcon />}
                onClick={handleClick}
                title="Export"
                disabled={dsrWsrData && dsrWsrData?.data?.length === 0}
              />
              <Popover
                className="export-popover"
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
              >
                <Box className="export-option">
                  <Typography
                    className="title-text fw600 pb8 cursor-pointer"
                    onClick={() => {
                      const filterData = {
                        dateSelectedOption,
                        customStartDate:
                          formatDateForComparison(customStartDate),
                        customEndDate: formatDateForComparison(customEndDate),
                        selectedBranches,
                        selectedMainCategories,
                        selectedSubcategories,
                        selectedOption,
                      };

                      handleExportWithProgress(
                        () =>
                          getExportDSRReport(
                            filterData,
                            dsrWsrData?.columns_group,
                            'pdf'
                          ),
                        'pdf',
                        'dsr'
                      );
                    }}
                  >
                    PDF
                  </Typography>
                  <Typography
                    className="title-text fw600 pb8 cursor-pointer"
                    onClick={() => {
                      const filterData = {
                        dateSelectedOption,
                        customStartDate:
                          formatDateForComparison(customStartDate),
                        customEndDate: formatDateForComparison(customEndDate),
                        selectedBranches,
                        selectedMainCategories,
                        selectedSubcategories,
                        selectedOption,
                      };

                      handleExportWithProgress(
                        () =>
                          getExportDSRReport(
                            filterData,
                            dsrWsrData?.columns_group,
                            'excel'
                          ),
                        'excel',
                        'dsr'
                      );
                    }}
                  >
                    Excel
                  </Typography>
                  <Typography
                    className="title-text fw600 cursor-pointer"
                    onClick={() => {
                      const filterData = {
                        dateSelectedOption,
                        customStartDate:
                          formatDateForComparison(customStartDate),
                        customEndDate: formatDateForComparison(customEndDate),
                        selectedBranches,
                        selectedMainCategories,
                        selectedSubcategories,
                        selectedOption,
                      };

                      handleExportWithProgress(
                        () =>
                          getExportDSRReport(
                            filterData,
                            dsrWsrData?.columns_group,
                            'csv'
                          ),
                        'csv',
                        'dsr'
                      );
                    }}
                  >
                    CSV
                  </Typography>
                </Box>
              </Popover>
            </Box>
          )}
        </Box>
        {/* section-right-content */}
        <Box className=" dsr-tabs-content pt32">{getCurrentContent()}</Box>
      </Box>
      <RightDrawer
        anchor={'right'}
        open={openFilterDrawer}
        onClose={handleCloseFIlterDrawer}
        title={tab === 1 || tab === '1' ? 'General Filters' : 'DSR/WSR Filters'}
        content={
          <StoredFilter
            ReportList={tab === 1 || tab === '1' ? ReportListGEN : ReportList}
            getReportsList={getReportsList}
            getReportByID={getReportByID}
            tab={tab}
          />
        }
      />
      <RightDrawer
        anchor={'right'}
        open={openSaveDrawer}
        onClose={handleSaveCloseDrawer}
        title={'Save Reports'}
        content={
          <Box className="Group-name-selection">
            <CustomTextField
              fullWidth
              id="ename"
              name="ename"
              value={
                tab === 1 || tab === '1'
                  ? ReportNameGEN
                    ? ReportNameGEN
                    : ''
                  : ReportName
                    ? ReportName
                    : ''
              }
              label="Report Name"
              required
              placeholder="Enter Report Name"
              onChange={(e) => {
                tab === 1 || tab === '1'
                  ? setReportNameGEN(e.target.value)
                  : setReportName(e.target.value);
              }}
            />
            <Box className="create-cancel-button pt32 justify-center">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => {
                  handleSaveCloseDrawer();
                }}
              />
              <CustomButton
                fullWidth
                variant="contained"
                title={'Save'}
                onClick={() => {
                  // groupSelectedBlocks(ReportName);
                  tab === 1 || tab === '1'
                    ? SaveReportGen(
                        {
                          dateCustSelOptionGEN,
                          customMonthGEN: customMonthGEN,
                          selectedBranchesGEN,
                          selectedMainCategoriesGEN,
                          selectedSubcategoriesGEN,
                          selectedOptionGEN,
                        },
                        GeneralData?.columns_group,
                        ReportNameGEN
                      )
                    : saveReportDSR(
                        {
                          dateSelectedOption,
                          customStartDate:
                            formatDateForComparison(customStartDate),
                          customEndDate: formatDateForComparison(customEndDate),
                          selectedBranches,
                          selectedMainCategories,
                          selectedSubcategories,
                          selectedOption,
                        },
                        dsrWsrData?.columns_group,
                        ReportName
                      );
                  handleSaveCloseDrawer();
                  tab === 1 || tab === '1'
                    ? setReportNameGEN('')
                    : setReportName('');
                }}
              />
            </Box>
          </Box>
        }
      />
      <RightDrawer
        className={''}
        anchor={'right'}
        open={openDrawer}
        onClose={handleCloseDrawer}
        title={'Columns'}
        content={
          <>
            <GroupingReorder
              onClose={handleCloseDrawer}
              GenGroupData={
                tab === 1 || tab === '1' ? GenGroupData : dsrWsrGroupData
              }
              getDSRReportFilter={
                tab === 1 || tab === '1'
                  ? getGeneralReportFilter
                  : getDSRReportFilter
              }
              setAppliedFilter={
                tab === 1 || tab === '1'
                  ? setAppliedFilterGEN
                  : setAppliedFilter
              }
              filterData={
                tab === 1 || tab === '1'
                  ? {
                      dateCustSelOptionGEN:
                        AppliedFilterGEN?.dateCustSelOptionGEN,
                      customMonthGEN: AppliedFilterGEN?.customMonthGEN,
                      selectedBranches: AppliedFilterGEN?.selectedBranches,
                      selectedMainCategories:
                        AppliedFilterGEN?.selectedMainCategories,
                      selectedSubcategories:
                        AppliedFilterGEN?.selectedSubcategories,
                      selectedOption: AppliedFilterGEN?.selectedOption,
                    }
                  : {
                      dateSelectedOption: AppliedFilter?.dateSelectedOption,
                      customStartDate: AppliedFilter?.customStartDate
                        ? AppliedFilter?.customStartDate
                        : formatDateForComparison(customStartDate),
                      customEndDate: AppliedFilter?.customEndDate
                        ? AppliedFilter?.customEndDate
                        : formatDateForComparison(customEndDate),
                      selectedBranches: AppliedFilter?.selectedBranches,
                      selectedMainCategories:
                        AppliedFilter?.selectedMainCategories,
                      selectedSubcategories:
                        AppliedFilter?.selectedSubcategories,
                      selectedOption: AppliedFilter?.selectedOption,
                    }
              }
            />
          </>
        }
      />

      <ExportStatusIndicator
        status={exportStatus}
        progress={exportProgress}
        fileName={exportFileName}
        onDownload={handleExportStatusDownload}
        onDismiss={handleExportDismiss}
        onRetry={handleExportRetry}
        exportError={exportError}
        estimatedTime={exportStatus === 'processing' ? '1-2 minutes' : null}
      />
    </>
  );
}
