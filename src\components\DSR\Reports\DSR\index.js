'use client';
import React from 'react';
import { Box, Tooltip, CircularProgress, Typography } from '@mui/material';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
// import BranchFilter from '../BranchFilter';
import DateFilter from '../DateFilter';
import CategoryFilter from '../CategoryFilter';
import TimePeriodFilter from '../TimePeriodsFilter';
import DoneOutlinedIcon from '@mui/icons-material/DoneOutlined';
import CloseIcon from '@mui/icons-material/Close';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import moment from 'moment';
import DSRTable from '../ReportTable/dsrTable';
import CustomButton from '@/components/UI/CustomButton';
import NoDataView from '@/components/UI/NoDataView';
import { DateFormat } from '@/helper/common/commonFunctions';
import MultipleFilter from '@/components/UI/MultipleFilter';

export default function DSRMSRPage({
  dsrCatData,
  // setDsrCatData,
  branchList,
  // setBranchList,
  selectedBranches,
  setSelectedBranches,
  selectedOption,
  setSelectedOption,
  customStartDate,
  setCustomStartDate,
  customEndDate,
  setCustomEndDate,
  selectedMainCategories,
  setSelectedMainCategories,
  dateSelectedOption,
  setDateSelectedOption,
  dateFilterList,
  // setDateFilterList,
  TimePeriodList,
  // setTimePeriodList,
  selectedSubcategories,
  setSelectedSubcategories,
  ActiveCate,
  setActiveCate,
  // setLoader,
  getDSRReportFilter,
  loader,
  dsrWsrData,
  getCategoriesPaymentList,
  setAppliedFilter,
  AppliedFilter,
  isBM,
  authState,
}) {
  const formatDateForComparison = (date) => {
    if (!date) return '';
    const d = new Date(date);
    const yyyy = d.getFullYear();
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const dd = String(d.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  };
  const sameArray = (array1, array2) => {
    return (
      array1.length === array2.length &&
      [...array1]
        .sort()
        .every((value, index) => value === [...array2].sort()[index])
    );
  };
  // Applies all selected filters
  const handleApplyFilter = () => {
    const filterData = {
      dateSelectedOption,
      customStartDate: formatDateForComparison(customStartDate),
      customEndDate: formatDateForComparison(customEndDate),
      selectedBranches,
      selectedMainCategories,
      selectedSubcategories,
      selectedOption,
      isGroup: AppliedFilter?.isGroup,
    };
    getDSRReportFilter(
      filterData,
      sameArray(AppliedFilter?.selectedSubcategories, selectedSubcategories) &&
        dsrWsrData?.data?.length > 0 &&
        AppliedFilter?.isGroup
        ? dsrWsrData?.columns_group
        : null
    );
    setAppliedFilter(filterData);
  };

  // Resets all filters to default values
  const handleCancelFilter = () => {
    setDateSelectedOption('today');
    setCustomStartDate(null);
    setCustomEndDate(null);
    setSelectedBranches(isBM ? [authState?.branch?.id] : []);
    setSelectedMainCategories([]);
    setSelectedSubcategories([]);
    setSelectedOption('none');
    const filterData = {
      dateSelectedOption: 'today',
      customStartDate: '',
      customEndDate: '',
      selectedBranches: isBM ? [authState?.branch?.id] : [],
      selectedMainCategories: [],
      selectedSubcategories: [],
      selectedOption: '',
    };
    getDSRReportFilter(filterData);
    setAppliedFilter(filterData);
  };
  const handleStartDateChange = (date) => {
    setCustomStartDate(date); // Set start date
  };

  const handleEndDateChange = (date) => {
    setCustomEndDate(date); // Set end date
  };

  // Applies date filter with selected date options
  const handleDateApplyFilter = () => {
    const filterData = {
      dateSelectedOption,
      customStartDate: formatDateForComparison(customStartDate),
      customEndDate: formatDateForComparison(customEndDate),
      selectedBranches,
      selectedMainCategories,
      selectedSubcategories,
      selectedOption,
      isGroup: AppliedFilter?.isGroup,
    };
    setAppliedFilter(filterData);
    getDSRReportFilter(
      filterData,
      sameArray(AppliedFilter?.selectedSubcategories, selectedSubcategories) &&
        dsrWsrData?.data?.length > 0 &&
        AppliedFilter?.isGroup
        ? dsrWsrData?.columns_group
        : null
    );
    setAppliedFilter(filterData);
    getDSRReportFilter(
      filterData,
      sameArray(AppliedFilter?.selectedSubcategories, selectedSubcategories) &&
        dsrWsrData?.data?.length > 0 &&
        AppliedFilter?.isGroup
        ? dsrWsrData?.columns_group
        : null
    );
  };
  const handleDateCancelFilter = () => {
    setCustomStartDate(null);
    setCustomEndDate(null);
  };

  const getDateByfilter = () => {
    if (dateSelectedOption === 'today') {
      return DateFormat(moment().startOf('day').toDate(), 'dates');
    } else if (dateSelectedOption === 'yesterday') {
      return DateFormat(
        moment().subtract(1, 'day').startOf('day').toDate(),
        'dates'
      );
    } else if (dateSelectedOption === 'this_week') {
      const start = DateFormat(moment().startOf('isoWeek').toDate(), 'dates');
      const end = DateFormat(moment().endOf('isoWeek').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_month') {
      const start = DateFormat(moment().startOf('month').toDate(), 'dates');
      const end = DateFormat(moment().endOf('month').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_quarter') {
      const start = DateFormat(moment().startOf('quarter').toDate(), 'dates');
      const end = DateFormat(moment().endOf('quarter').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_week') {
      const start = DateFormat(
        moment().subtract(1, 'week').startOf('isoWeek').toDate(),
        'dates'
      );
      const end = DateFormat(
        moment().subtract(1, 'week').endOf('isoWeek').toDate(),
        'dates'
      );
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_month') {
      const start = DateFormat(
        moment().subtract(1, 'month').startOf('month').toDate(),
        'dates'
      );
      const end = DateFormat(
        moment().subtract(1, 'month').endOf('month').toDate(),
        'dates'
      );
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_7_days') {
      const start = DateFormat(
        moment().subtract(7, 'days').startOf('day').toDate(),
        'dates'
      );
      const end = DateFormat(moment().endOf('day').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_14_days') {
      const start = DateFormat(
        moment().subtract(14, 'days').startOf('day').toDate(),
        'dates'
      );
      const end = DateFormat(moment().endOf('day').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_30_days') {
      const start = DateFormat(
        moment().subtract(30, 'days').startOf('day').toDate(),
        'dates'
      );
      const end = DateFormat(moment().endOf('day').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_60_days') {
      const start = DateFormat(
        moment().subtract(60, 'days').startOf('day').toDate(),
        'dates'
      );
      const end = DateFormat(moment().endOf('day').toDate(), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_90_days') {
      const start = DateFormat(
        moment().subtract(90, 'days').startOf('day').toDate(),
        'dates'
      );
      const end = DateFormat(moment().endOf('day').toDate(), 'dates');
      return `${start} - ${end}`;
    } else {
      return '';
    }
  };

  return (
    <Box>
      <Box className="filter-container">
        <Box
          className="subcategories-wrap"
          width="75%"
          sx={{ paddingLeft: '20px' }}
        >
          {/* TimePeriods Filter */}
          <DateFilter
            dateSelectedOption={dateSelectedOption}
            setDateSelectedOption={setDateSelectedOption}
            customStartDate={customStartDate}
            setCustomStartDate={setCustomStartDate}
            customEndDate={customEndDate}
            setCustomEndDate={setCustomEndDate}
            dateFilterList={dateFilterList}
            type="dsr"
          />

          {/* Branch Filter */}
          <MultipleFilter
            selected={selectedBranches}
            setSelected={setSelectedBranches}
            List={branchList}
            placeholder="Branches"
            disabled={isBM}
          />
          <Box className="category-section">
            <CustomButton
              variant="outlined"
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">
                      {ActiveCate ? 'All Categories' : 'Only Active Categories'}
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  {ActiveCate ? <ToggleOnIcon /> : <ToggleOffIcon />}
                </Tooltip>
              }
              onClick={() => {
                setActiveCate(!ActiveCate);
                getCategoriesPaymentList(
                  !ActiveCate,
                  dateSelectedOption,
                  '',
                  '',
                  isBM ? authState?.branch?.id : ''
                );
              }}
            />
            {/* Category Filter */}
            <CategoryFilter
              selectedMainCategories={selectedMainCategories}
              selectedSubcategories={selectedSubcategories}
              dsrCatData={dsrCatData}
              setSelectedMainCategories={setSelectedMainCategories}
              setSelectedSubcategories={setSelectedSubcategories}
            />
          </Box>
          {/* TimePeriod Filter */}
          <TimePeriodFilter
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
            TimePeriodList={TimePeriodList}
          />
          <Box className="d-flex gap-5">
            <CustomButton
              variant="contained"
              onClick={handleApplyFilter}
              title="Apply"
              disabled={
                dateSelectedOption === 'custom'
                  ? customStartDate && customEndDate
                    ? false
                    : true
                  : false
              }
            />
            <CustomButton
              variant="outlined"
              onClick={handleCancelFilter}
              title="Clear"
            />
          </Box>
        </Box>
      </Box>

      {getDateByfilter() ? (
        <span className="title-text">{getDateByfilter()}</span>
      ) : (
        <></>
      )}

      {dateSelectedOption === 'custom' && (
        <>
          <Box className="d-flex gap-5 align-end mt8">
            <Box className="d-flex gap-5">
              <Box>
                <CustomDatePicker
                  label="Start Date"
                  value={customStartDate}
                  onChange={handleStartDateChange}
                  fullWidth
                  disableFuture={true}
                  inputVariant="outlined"
                  error={false}
                />
              </Box>
              <Box>
                <CustomDatePicker
                  label="End Date"
                  value={customEndDate}
                  onChange={handleEndDateChange}
                  fullWidth
                  disableFuture={true}
                  inputVariant="outlined"
                  error={false}
                />
              </Box>
            </Box>

            <Box className="d-flex gap-5">
              <CustomButton
                variant="contained"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Filter
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <DoneOutlinedIcon sx={{ fontWeight: 700 }} />
                  </Tooltip>
                }
                onClick={() => {
                  customStartDate && customEndDate && handleDateApplyFilter();
                }}
              />
              <CustomButton
                variant="outlined"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">Cancel</Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                    arrow
                  >
                    <CloseIcon sx={{ fontWeight: 700 }} />
                  </Tooltip>
                }
                onClick={handleDateCancelFilter}
              />
            </Box>
          </Box>
        </>
      )}
      <Box className="pt24">
        {loader ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : dsrWsrData &&
          dsrWsrData?.data &&
          dsrWsrData?.data?.length > 0 &&
          dsrWsrData?.column &&
          Object.keys(dsrWsrData?.column).length > 1 ? (
          <DSRTable dsrWsrData={dsrWsrData} />
        ) : (
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              title="No DSR/WSR Reports Found"
              description="There is no dsr/wsr reports available at the moment."
              className="no-data-auto-margin-height-conainer"
            />
          </Box>
        )}
      </Box>
    </Box>
  );
}
