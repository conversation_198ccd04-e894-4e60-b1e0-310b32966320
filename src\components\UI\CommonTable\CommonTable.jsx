import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Menu,
  MenuItem,
  IconButton,
} from '@mui/material';
import { ArrowUpward, ArrowDownward, MoreVert } from '@mui/icons-material';
import CustomOrgPagination from '../customPagination';
import NoDataView from '../NoDataView';
import './CommonTable.scss';

function descendingComparator(a, b, orderBy) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

function stableSort(array, comparator) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
}

// ActionMenu component for handling dynamic menu items
const ActionMenu = ({ menuItems, row }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    event.stopPropagation(); // Prevent row click
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (menuItem, row) => {
    handleClose();
    menuItem?.onClick?.(menuItem, row);
  };

  return (
    <>
      <IconButton
        size="small"
        onClick={handleClick}
        // sx={{ padding: '4px' }}
        aria-label="actions"
        aria-controls={open ? 'action-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <MoreVert fontSize="small" />
      </IconButton>
      <Menu
        id="action-menu"
        className="table-action-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        onClick={(e) => e.stopPropagation()} // Prevent row click
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {menuItems?.map?.((menuItem, index) => (
          <MenuItem
            key={index}
            onClick={() => handleMenuItemClick(menuItem, row)}
            disabled={menuItem?.disabled}
            // sx={{
            //   fontSize: '0.875rem',
            //   minWidth: '140px',
            //   display: 'flex',
            //   alignItems: 'center',
            //   gap: 1,
            //   ...(menuItem?.sx || {}),
            // }}
            className={menuItem?.variant === 'danger' ? 'danger' : ''}
            disableRipple
          >
            {menuItem?.icon && (
              <Box
                component="span"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {menuItem.icon}
              </Box>
            )}
            <span>{menuItem?.label}</span>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

const CommonTable = ({
  columns,
  data,
  pageSize = 10,
  onRowClick,
  actions,
  actionMenuItems, // New prop for menu items configuration
  paginationProps = {},
  // Additional props for backward compatibility
  currentPage,
  totalCount,
  onPageChange,
  onRowsPerPageChange,
  showPagination = true,
  onSort, // New prop for server-side sorting
  sortOrder, // Current sort order for server-side sorting
}) => {
  // External pagination support
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState(columns?.[0]?.accessor || '');
  const [page, setPage] = useState(currentPage || 1); // Use external currentPage if provided
  const [rowsPerPage, setRowsPerPage] = useState(pageSize);

  // Use external pagination if provided, otherwise use internal
  const isExternalPagination =
    currentPage !== undefined && totalCount !== undefined;

  const handleRequestSort = (property, event) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // If onSort prop is provided, use server-side sorting
    if (onSort) {
      // Find the column to get the sortKey
      const column = columns?.find((col) => col?.accessor === property);
      const sortKey = column?.sortKey || property;
      onSort(sortKey);
    } else {
      // Use client-side sorting
      const isAsc = orderBy === property && order === 'asc';
      setOrder(isAsc ? 'desc' : 'asc');
      setOrderBy(property);
    }
  };

  const handleChangePage = (newPage) => {
    setPage(newPage);
    // Call external handler if provided, otherwise use paginationProps
    if (onPageChange) {
      onPageChange(newPage);
    } else {
      paginationProps?.onPageChange?.(newPage);
    }
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(Number(newRowsPerPage));
    setPage(1);
    // Call external handler if provided, otherwise use paginationProps
    if (onRowsPerPageChange) {
      onRowsPerPageChange(Number(newRowsPerPage));
    } else {
      paginationProps?.onRowsPerPageChange?.(Number(newRowsPerPage));
    }
  };

  // Use external pagination or internal pagination
  const paginatedData = isExternalPagination
    ? data || [] // For external pagination, data is already paginated
    : stableSort(data || [], getComparator(order, orderBy)).slice(
        (page - 1) * rowsPerPage,
        (page - 1) * rowsPerPage + rowsPerPage
      );

  // Render actions based on whether it's a function or menu items
  const renderActions = (row) => {
    if (actionMenuItems) {
      // If actionMenuItems is a function, call it with row data, otherwise use as static array
      const menuItems =
        typeof actionMenuItems === 'function'
          ? actionMenuItems(row)
          : actionMenuItems;
      if (menuItems?.length) {
        return <ActionMenu menuItems={menuItems} row={row} />;
      }
    }
    if (actions) {
      return actions?.(row);
    }
    return null;
  };

  return (
    <Paper className="common-table-container">
      <TableContainer>
        <Table size="small" aria-label="a dense table">
          <TableHead>
            <TableRow>
              {columns?.map?.((col) => (
                <TableCell
                  key={col?.accessor}
                  sortDirection={orderBy === col?.accessor ? order : false}
                >
                  {col?.sortable ? (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                      }}
                      onClick={(event) =>
                        handleRequestSort(col?.accessor, event)
                      }
                    >
                      {col?.header}
                      {/* Show sort arrows based on server-side or client-side sorting */}
                      {onSort && sortOrder ? (
                        // Server-side sorting: show arrows for all sortable columns
                        sortOrder?.key === (col?.sortKey || col?.accessor) ? (
                          sortOrder?.value === 'ASC' ? (
                            <ArrowUpward
                              sx={{
                                ml: 0.5,
                                fontSize: '1rem',
                                color: 'white',
                                pointerEvents: 'none',
                              }}
                            />
                          ) : (
                            <ArrowDownward
                              sx={{
                                ml: 0.5,
                                fontSize: '1rem',
                                color: 'white',
                                pointerEvents: 'none',
                              }}
                            />
                          )
                        ) : (
                          // Show default up arrow for non-active sortable columns
                          <ArrowUpward
                            sx={{
                              ml: 0.5,
                              fontSize: '1rem',
                              color: 'white',
                              pointerEvents: 'none',
                            }}
                          />
                        )
                      ) : // Client-side sorting: use internal state
                      orderBy === col?.accessor ? (
                        order === 'asc' ? (
                          <ArrowUpward
                            sx={{
                              ml: 0.5,
                              fontSize: '1rem',
                              color: 'white',
                              pointerEvents: 'none',
                            }}
                          />
                        ) : (
                          <ArrowDownward
                            sx={{
                              ml: 0.5,
                              fontSize: '1rem',
                              color: 'white',
                              pointerEvents: 'none',
                            }}
                          />
                        )
                      ) : (
                        // Show default up arrow for non-active sortable columns
                        <ArrowUpward
                          sx={{
                            ml: 0.5,
                            fontSize: '1rem',
                            color: 'white',
                            pointerEvents: 'none',
                          }}
                        />
                      )}
                    </Box>
                  ) : (
                    col?.header
                  )}
                </TableCell>
              ))}
              {(actions || actionMenuItems?.length !== 0) && (
                <TableCell>Actions</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedData && paginatedData?.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns?.length + 1}>
                  <NoDataView
                    title="No Report Data Found"
                    description="There is no report data available at the moment."
                    className="no-data-auto-margin-height-conainer"
                  />
                </TableCell>
              </TableRow>
            ) : (
              paginatedData?.map?.((row, idx) => (
                <TableRow
                  key={idx}
                  hover
                  className="common-table-row"
                  onClick={onRowClick ? () => onRowClick?.(row) : undefined}
                  style={{ cursor: onRowClick ? 'pointer' : 'default' }}
                >
                  {columns?.map?.((col) => (
                    <TableCell key={col?.accessor}>
                      {col?.renderCell
                        ? col?.renderCell(row?.[col?.accessor], row)
                        : row?.[col?.accessor]}
                    </TableCell>
                  ))}
                  {(actions || actionMenuItems?.length !== 0) && (
                    <TableCell>{renderActions(row)}</TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {paginatedData?.length > 0 && showPagination && (
        <Box className="common-table-pagination">
          <CustomOrgPagination
            currentPage={isExternalPagination ? currentPage : page}
            totalCount={isExternalPagination ? totalCount : data?.length || 0}
            rowsPerPage={rowsPerPage}
            onPageChange={handleChangePage}
            OnRowPerPage={handleRowsPerPageChange}
            {...paginationProps}
          />
        </Box>
      )}
    </Paper>
  );
};

export default CommonTable;
