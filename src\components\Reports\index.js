'use client';
import React, {
  useState,
  useLayoutEffect,
  useCallback,
  useRef,
  useEffect,
} from 'react';
import { Box, Divider, Popover, Tooltip, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import CustomTabs from '@/components/UI/CustomTabs';
import { useRouter, useSearchParams } from 'next/navigation';
import { reportsMenuList } from '@/helper/common/commonMenus';
import CustomButton from '../UI/CustomButton';
import DownloadIcon from '@mui/icons-material/Download';
// import { setApiMessage } from '@/helper/common/commonFunctions';
import { reportsService } from '@/services/reportService';
import ExportStatusIndicator from '@/components/Users/<USER>/DownloadField/components/ExportProgress';
import './reports.scss';

const Reports = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeMenuItem, setActiveMenuItem] = useState(1);
  const [activeTab, setActiveTab] = useState(1);

  const isReport = searchParams.get('is_report');
  const isActiveTab = searchParams.get('is_tab');
  const queryParams = new URLSearchParams(searchParams);

  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [currentFilters, setCurrentFilters] = useState({});
  const exportOpen = Boolean(exportAnchorEl);
  const exportId = exportOpen ? 'export-popover' : undefined;

  // Export status management
  const [exportStatus, setExportStatus] = useState('idle'); // 'idle', 'processing', 'completed', 'error'
  const [exportProgress, setExportProgress] = useState(0);
  const [exportFileName, setExportFileName] = useState('');
  const [exportError, setExportError] = useState('');
  const [downloadUrl, setDownloadUrl] = useState('');
  const [currentExportFormat, setCurrentExportFormat] = useState('');

  // Ref to store progress interval for cleanup
  const progressIntervalRef = useRef(null);

  // Initialize from URL parameters
  useLayoutEffect(() => {
    setActiveMenuItem(Number(isReport || 1));
  }, [isReport]);

  useLayoutEffect(() => {
    const tabIndex = Number(isActiveTab) || 1;
    setActiveTab(tabIndex);
  }, [isActiveTab]);

  // Reset filters when menu or tab changes
  useLayoutEffect(() => {
    setCurrentFilters({});
  }, [activeMenuItem, activeTab]);

  // Cleanup progress interval on unmount
  useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    };
  }, []);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
    setActiveTab(1); // Reset to first tab when menu item changes
    router.push(`/reports?is_report=${item?.id}&is_tab=1`);
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    queryParams.set('is_tab', tabId);
    router.push(`?${queryParams.toString()}`);
  };

  const getCurrentTabs = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );
    return currentItem?.tabs || [];
  };

  // Callback function for child components to update filters
  const handleFiltersUpdate = useCallback((filters) => {
    setCurrentFilters(filters);
  }, []);

  // Export status handlers
  const handleExportStatusDownload = () => {
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', exportFileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleExportDismiss = () => {
    // Clear any active progress interval
    if (progressIntervalRef.current) {
      clearTimeout(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }

    setExportStatus('idle');
    setExportProgress(0);
    setExportFileName('');
    setExportError('');
    setDownloadUrl('');
    setCurrentExportFormat('');
  };

  const handleExportRetry = () => {
    if (currentExportFormat) {
      handleExportDownload(currentExportFormat);
    }
  };

  const getCurrentComponent = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );

    // If item has tabs, check for tab-specific component
    if (currentItem?.tabs && currentItem.tabs.length > 0) {
      const currentTab = currentItem.tabs.find((tab) => tab.id === activeTab);
      const Component = currentTab?.component || currentItem?.component;

      // Clone the component and pass the filters update callback
      if (Component) {
        return React.cloneElement(Component, {
          onFiltersUpdate: handleFiltersUpdate,
          key: `${activeMenuItem}-${activeTab}`, // Add key to force re-render when tab changes
        });
      }
      return null;
    }

    // If no tabs, return main component with callback
    const Component = currentItem?.component;
    if (Component) {
      return React.cloneElement(Component, {
        onFiltersUpdate: handleFiltersUpdate,
        key: activeMenuItem, // Add key to force re-render when menu changes
      });
    }
    return null;
  };

  const handleExportClick = (event) => {
    setExportAnchorEl(event.currentTarget);
  };

  const handleExportClose = () => {
    setExportAnchorEl(null);
  };

  const handleExportDownload = async (format) => {
    try {
      // Clear any existing progress interval
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      // Set initial export status
      setExportStatus('processing');
      setExportProgress(0);
      setCurrentExportFormat(format);
      setExportError('');
      handleExportClose();

      // Generate default filename
      const reportNames = {
        1: 'rota_report',
        2: 'document_report',
        3: 'activity_report',
        4: activeTab === 1 ? 'staff_user_report' : 'change_request_report',
        5: 'log_book_report',
        6:
          activeTab === 1 ? 'leave_balance_report' : 'leave_consumption_report',
        7: activeTab === 1 ? 'recipe_cta_analytics' : 'contact_submissions',
      };

      const defaultFilename = `${reportNames[activeMenuItem] || 'report'}_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : format}`;
      setExportFileName(defaultFilename);

      // Auto-manage progress with realistic simulation
      const progressSteps = [
        { progress: 15, delay: 200 },
        { progress: 35, delay: 500 },
        { progress: 55, delay: 800 },
        { progress: 75, delay: 600 },
      ];

      // Start progress simulation
      let stepIndex = 0;
      const updateProgress = () => {
        if (stepIndex < progressSteps.length) {
          const step = progressSteps[stepIndex];
          setExportProgress(step.progress);
          stepIndex++;

          progressIntervalRef.current = setTimeout(updateProgress, step.delay);
        }
      };

      // Start progress updates
      updateProgress();

      // Use the generic export service with current filters
      const response = await reportsService.exportReport(
        activeMenuItem,
        activeTab,
        format,
        currentFilters // Pass current filters from active component
      );

      // Clear any remaining progress intervals
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      if (response.success) {
        let filename = defaultFilename;

        // Get filename from response headers if available
        const disposition = response?.headers?.['content-disposition'];
        if (disposition) {
          const match = disposition.match(/filename="?([^";]+)"?/);
          if (match) {
            filename = match[1];
          }
        }

        // Final progress steps for completion
        setExportProgress(90);

        // Create blob and store download URL
        const blob = response.data;
        const url = window.URL.createObjectURL(blob);

        // Complete the progress
        setTimeout(() => {
          setDownloadUrl(url);
          setExportFileName(filename);
          setExportProgress(100);
          setExportStatus('completed');
          // setApiMessage('success', 'Export completed successfully');
        }, 300);
      } else {
        setExportStatus('error');
        setExportError(response.error || 'Export failed');
        // setApiMessage('error', response.error || 'Export failed');
      }
    } catch (error) {
      // Clear progress interval on error
      if (progressIntervalRef.current) {
        clearTimeout(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      console.error('Export error:', error);
      setExportStatus('error');
      setExportError(error?.message || 'Export failed');
      // setApiMessage('error', error?.message || 'Export failed');
    }
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        {/* Show tabs only if current menu item has tabs */}
        {getCurrentTabs().length > 0 && (
          <Box className="section-right-tab-header">
            <Box className="report-header-tabs">
              <Box className="report-header-tabs-wrap">
                <CustomTabs
                  tabs={getCurrentTabs()}
                  initialTab={activeTab}
                  onTabChange={handleTabChange}
                />
              </Box>
              {/* Hide export button for menu items 2 and 5 */}
              {activeMenuItem !== 2 && activeMenuItem !== 5 && (
                <CustomButton
                  variant="outlined"
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Export Report
                        </Typography>
                      }
                      arrow
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <DownloadIcon />
                    </Tooltip>
                  }
                  onClick={handleExportClick}
                />
              )}
            </Box>
          </Box>
        )}
        <Box className="section-right-content">{getCurrentComponent()}</Box>
      </Box>

      <ExportStatusIndicator
        status={exportStatus}
        progress={exportProgress}
        fileName={exportFileName}
        onDownload={handleExportStatusDownload}
        onDismiss={handleExportDismiss}
        onRetry={handleExportRetry}
        exportError={exportError}
        estimatedTime={exportStatus === 'processing' ? '1-2 minutes' : null}
      />

      <Popover
        className="export-popover"
        id={exportId}
        open={exportOpen}
        anchorEl={exportAnchorEl}
        onClose={handleExportClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Box className="export-option">
          <Typography
            className="title-text pb8 cursor-pointer fw600"
            onClick={() => handleExportDownload('pdf')}
          >
            PDF
          </Typography>
          <Typography
            className="title-text pb8 cursor-pointer fw600"
            onClick={() => handleExportDownload('excel')}
          >
            Excel
          </Typography>
          <Typography
            className="title-text cursor-pointer fw600"
            onClick={() => handleExportDownload('csv')}
          >
            CSV
          </Typography>
        </Box>
      </Popover>
    </Box>
  );
};

export default Reports;
